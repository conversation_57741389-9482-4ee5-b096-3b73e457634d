<apex:page standardController="Quote" extensions="QuotePrintPDFController" renderAs="pdf" showHeader="false" sidebar="false" applyHtmlTag="false">
    
    
    <html>
        <head>
            <style type="text/css">
                body {
                    font-family: Arial Unicode MS, sans-serif;
                    font-size: 12px;
                    margin: 0;
                    padding: 0;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    border: 1px solid #000;
                    margin: 0;
                    padding: 0;
                    border-spacing: 0;
                }
                
                th, td {
                    border: 1px solid #000;
                    padding: 5px;
                    text-align: left;
                    vertical-align: top;
                    margin: 0;
                    line-height: 1.2;
                }

                tr {
                    margin: 0;
                    padding: 0;
                    line-height: 1.2;
                }

                .product-table {
                    border-spacing: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                .product-table tr {
                    margin: 0 !important;
                    padding: 0 !important;
                    height: auto !important;
                }

                .product-table td {
                    margin: 0 !important;
                    padding: 3px 5px !important;
                    line-height: 1.1 !important;
                }
                
                .title {
                    text-align: center;
                    font-size: 20px;
                    font-weight: bold;
                    margin: 10px 0;
                }
                
                .subtitle {
                    font-weight: bold;
                    margin-top: 10px;
                    margin-bottom: 5px;
                }
                
                .signature-area {
                    margin-top: 30px;
                }
                
                .terms {
                    margin-top: 10px;
                    margin-bottom: 10px;
                }
                
                .company-info {
                    font-size: 11px;
                    margin-top: 5px;
                }
                
                .small-text {
                    font-size: 10px;
                }
                
                .contract-info {
                    text-align: right;
                    margin-bottom: 5px;
                }
                
                .section-number {
                    font-weight: bold;
                }
                
                .header-row {
                    background-color: #f2f2f2;
                }
                 .contract-paragraph {
                    margin-bottom: 15px;
                    line-height: 1.5;
                }

                /* 客户账号ID列样式 - 限制宽度并启用换行 */
                .account-id-cell {
                    width: 80px;
                    max-width: 80px;
                    font-size: 8px;
                    line-height: 1.1;
                    padding: 1px 2px;
                    vertical-align: top;
                    text-align: left;
                }

                /* 强制换行的样式 */
                .break-word {
                    word-break: break-all;
                    white-space: normal;
                }
            </style>
        </head>
        <body>
           
            <table>
                <tr>
                    <td rowspan="5" width="30%" class="section-number">1.{!$Label.Customer_Information}</td>
                    <td colspan="2" width="30%">{!$Label.Order_Number}：</td>
                    <td colspan="2" width="40%">{!quote.QuoteNumber}</td>
                </tr>
            </table>
            
           
            <table>
                <tr>               
                    <td rowspan="5" width="20%">{!$Label.Company_Information}</td>
                    <td width="15%">{!$Label.Company_Name}：</td>
                    <td width="30%">{!quote.Account.Name}</td>
                    <td width="15%">{!$Label.Legal_Representative}：</td>
                    <td width="15%">{!quote.Account.Legal_Representative__c}</td>
                </tr>
                <tr>               
                    <td>{!$Label.Registered_Address}：</td>
                    <td colspan="3">{!quote.Account.CompanyAddress__c}</td>
                </tr>
                <tr>
                    <td>{!$Label.ICP_Number}：</td>
                    <td>{!quote.Account.Customer_ICP_Filing_Number__c}</td>
                    <td>{!$Label.Business_License_Number}：</td>
                    <td>{!quote.Account.Business_License_Number__c}</td>
                </tr>
                <tr>
                    <td rowspan="2">{!$Label.Address}：</td>
                    <!-- <td rowspan="2">{!quote.Account.Customer_Mailing_Address__c}</td> -->
                    <td rowspan="2">{!quote.Account.CompanyAddress__c}</td>
                    <td >{!$Label.Postal_Code}：</td>
                    <td >{!quote.Account.PostalCode__c}</td>
                </tr>
               
            </table>
            
            <!-- 2. 申请服务内容 -->
            <!-- <table>
                <tr>
                    <td colspan="6" class="section-number">2.{!$Label.Application_Service_Content}：</td>
                </tr>
                <tr>
                    <td colspan="6">{!$Label.Accelerate_Domain_Name}：</td>
                </tr>
                <tr>
                    <td width="15%">{!$Label.Domain_Name}</td>
                    <td width="25%">{!$Label.Domain_Description}</td>
                    <td width="20%">{!$Label.Service_Category}</td>
                    <td width="15%">{!$Label.Expected_Bandwidth}</td>
                    <td width="25%">{!$Label.Test_URL}</td>
                </tr>
                <tr>
                    <td>{!quote.Account.Customer_Domain__c}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table> -->
                 
            <table>
                <tr>
                    <td colspan="10">{!$Label.Configuration_Service}：</td>
                </tr>
            </table>
            <!-- 产品折扣 -->
            <apex:outputPanel layout="none" rendered="{!quoteLineItemToProductDiscountWrappers.size != 0}">
                <table class="product-table" style="margin-bottom: 5px; border-collapse: collapse; width: 100%; table-layout: fixed;">
                    <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                        <tr>
                            <td width="20%">{!$Label.Product_Name}</td>
                            <td width="8%">牌价</td>
                            <td width="12%">{!$Label.Customer_AccountID}</td>
                            <td width="8%">区域</td>
                            <td width="10%">起始日期</td>
                            <td width="10%">结束日期</td>
                            <td width="8%">{!$Label.Discount_Factor}</td>
                            <td width="8%">{!$Label.Fixed_Rebate}</td>
                            <td width="8%">{!$Label.Cash_Reduction}</td>
                            <td width="8%">{!$Label.Credit}</td>
                        </tr>
                    </apex:outputPanel>
                    <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                        <tr>
                            <td width="25%">{!$Label.Product_Name}</td>
                            <td width="10%">牌价</td>
                            <td width="10%">区域</td>
                            <td width="12%">起始日期</td>
                            <td width="12%">结束日期</td>
                            <td width="8%">{!$Label.Discount_Factor}</td>
                            <td width="8%">{!$Label.Fixed_Rebate}</td>
                            <td width="8%">{!$Label.Cash_Reduction}</td>
                            <td width="7%">{!$Label.Credit}</td>
                        </tr>
                    </apex:outputPanel>

                    <apex:repeat value="{!quoteLineItemToProductDiscountWrappers}" var="wrapper">
                        <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <tr>
                                <td width="20%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="8%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="12%" class="account-id-cell">
                                    <apex:outputText value="{!wrapper.formattedAccountId}" escape="false" />
                                </td>
                                <td width="8%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="10%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="10%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="8%">{!wrapper.quoteMethod.Discount_Factor__c}</td>
                                <td width="8%">{!wrapper.quoteMethod.Fixed_Rebate__c}</td>
                                <td width="8%">{!wrapper.quoteMethod.Cash_Reduce__c}</td>
                                <td width="8%">{!wrapper.quoteMethod.Credit__c}</td>
                            </tr>
                        </apex:outputPanel>
                        <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                            <tr>
                                <td width="25%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="10%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="10%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="12%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="12%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="8%">{!wrapper.quoteMethod.Discount_Factor__c}</td>
                                <td width="8%">{!wrapper.quoteMethod.Fixed_Rebate__c}</td>
                                <td width="8%">{!wrapper.quoteMethod.Cash_Reduce__c}</td>
                                <td width="7%">{!wrapper.quoteMethod.Credit__c}</td>
                            </tr>
                        </apex:outputPanel>
                    </apex:repeat>
                </table>
            </apex:outputPanel>
            <!-- 单价*数量 -->
            <apex:outputPanel layout="none" rendered="{!quoteLineItemToUnitAmountWrappers.size != 0}">
                <table class="product-table" style=" border-collapse: collapse; width: 100%; table-layout: fixed;">
                    <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                        <tr>
                            <td width="25%">{!$Label.Product_Name}</td>
                            <td width="15%">{!$Label.Customer_AccountID}</td>
                            <td width="10%">区域</td>
                            <td width="12%">起始日期</td>
                            <td width="12%">结束日期</td>
                            <td width="13%">{!$Label.Minimum_Price}</td>
                            <td width="13%">{!$Label.Minimum_Quantity}</td>
                        </tr>
                    </apex:outputPanel>
                    <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                        <tr>
                            <td width="30%">{!$Label.Product_Name}</td>
                            <td width="15%">区域</td>
                            <td width="15%">起始日期</td>
                            <td width="15%">结束日期</td>
                            <td width="12.5%">{!$Label.Minimum_Price}</td>
                            <td width="12.5%">{!$Label.Minimum_Quantity}</td>
                        </tr>
                    </apex:outputPanel>

                    <apex:repeat value="{!quoteLineItemToUnitAmountWrappers}" var="wrapper">
                        <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <tr>
                                <td width="25%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="15%" class="account-id-cell">
                                    <apex:outputText value="{!wrapper.formattedAccountId}" escape="false" />
                                </td>
                                <td width="10%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="12%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="12%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="13%">{!wrapper.quoteMethod.Minimum_Amout__c}</td>
                                <td width="13%">{!wrapper.quoteMethod.Minimum_ProdUnitPrice__c}</td>
                            </tr>
                        </apex:outputPanel>
                        <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                            <tr>
                                <td width="30%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="15%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="15%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="15%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="12.5%">{!wrapper.quoteMethod.Minimum_Amout__c}</td>
                                <td width="12.5%">{!wrapper.quoteMethod.Minimum_ProdUnitPrice__c}</td>
                            </tr>
                        </apex:outputPanel>
                    </apex:repeat>
                </table>
            </apex:outputPanel>

             <!-- 阶梯用量单价-->
            <apex:outputPanel layout="none" rendered="{!quoteLineItemToUsageLadderWrappers.size != 0}">
                <table class="product-table" style="margin-bottom: 5px; border-collapse: collapse; width: 100%; table-layout: fixed;">
                    <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                        <tr>
                            <td width="25%">{!$Label.Product_Name}</td>
                            <td width="15%">{!$Label.Customer_AccountID}</td>
                            <td width="12%">牌价</td>
                            <td width="10%">区域</td>
                            <td width="19%">起始日期</td>
                            <td width="19%">结束日期</td>
                        </tr>
                    </apex:outputPanel>
                    <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                        <tr>
                            <td width="30%">{!$Label.Product_Name}</td>
                            <td width="15%">牌价</td>
                            <td width="15%">区域</td>
                            <td width="20%">起始日期</td>
                            <td width="20%">结束日期</td>
                        </tr>
                    </apex:outputPanel>

                    <apex:repeat value="{!quoteLineItemToUsageLadderWrappers}" var="wrapper">
                        <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <tr>
                                <td width="25%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="15%" class="account-id-cell">
                                    <apex:outputText value="{!wrapper.formattedAccountId}" escape="false" />
                                </td>
                                <td width="12%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="10%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="19%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="19%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                            </tr>
                        </apex:outputPanel>
                        <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                            <tr>
                                <td width="30%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="15%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="15%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="20%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="20%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                            </tr>
                        </apex:outputPanel>
                    </apex:repeat>
                </table>
            </apex:outputPanel>

             <!-- 保底金额+共享阶梯金额折扣-->
            <apex:outputPanel layout="none" rendered="{!quoteLineItemToMinAmountSharedAmountDiscountWrappers.size != 0}">
                <table class="product-table" style="margin-bottom: 5px; border-collapse: collapse; width: 100%; table-layout: fixed;">
                    <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                        <tr>
                            <td width="22%">{!$Label.Product_Name}</td>
                            <td width="13%">{!$Label.Customer_AccountID}</td>
                            <td width="10%">区域</td>
                            <td width="12%">牌价</td>
                            <td width="15%">起始日期</td>
                            <td width="15%">结束日期</td>
                            <td width="13%">保底金额</td>
                        </tr>
                    </apex:outputPanel>
                    <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                        <tr>
                            <td width="28%">{!$Label.Product_Name}</td>
                            <td width="12%">区域</td>
                            <td width="15%">牌价</td>
                            <td width="17%">起始日期</td>
                            <td width="17%">结束日期</td>
                            <td width="11%">保底金额</td>
                        </tr>
                    </apex:outputPanel>

                    <apex:repeat value="{!quoteLineItemToMinAmountSharedAmountDiscountWrappers}" var="wrapper">
                        <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <tr>
                                <td width="22%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="13%" class="account-id-cell">
                                    <apex:outputText value="{!wrapper.formattedAccountId}" escape="false" />
                                </td>
                                <td width="10%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="12%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="15%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="15%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="13%">{!wrapper.quoteMethod.GuaranteedMin_Amount__c}</td>
                            </tr>
                        </apex:outputPanel>
                        <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                            <tr>
                                <td width="28%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="12%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="15%">
                                    <apex:outputPanel rendered="{!wrapper.listPrice != null}">
                                        {!wrapper.listPrice} {!wrapper.listPriceCurrency}
                                    </apex:outputPanel>
                                </td>
                                <td width="17%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="17%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="11%">{!wrapper.quoteMethod.GuaranteedMin_Amount__c}</td>
                            </tr>
                        </apex:outputPanel>
                    </apex:repeat>
                </table>
            </apex:outputPanel>

             <!-- 单价*数量+共享阶梯用量单价-->
            <apex:outputPanel layout="none" rendered="{!quoteLineItemToUnitAmountSharedUsageLadderWrappers.size != 0}">
                <table class="product-table" style="margin-bottom: 5px; border-collapse: collapse; width: 100%; table-layout: fixed;">
                    <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                        <tr>
                            <td width="20%">{!$Label.Product_Name}</td>
                            <td width="13%">{!$Label.Customer_AccountID}</td>
                            <td width="12%">{!$Label.Minimum_Price}</td>
                            <td width="12%">{!$Label.Minimum_Quantity}</td>
                            <td width="10%">区域</td>
                            <td width="16.5%">起始日期</td>
                            <td width="16.5%">结束日期</td>
                        </tr>
                    </apex:outputPanel>
                    <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                    <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                        <tr>
                            <td width="25%">{!$Label.Product_Name}</td>
                            <td width="15%">{!$Label.Minimum_Price}</td>
                            <td width="15%">{!$Label.Minimum_Quantity}</td>
                            <td width="12%">区域</td>
                            <td width="16.5%">起始日期</td>
                            <td width="16.5%">结束日期</td>
                        </tr>
                    </apex:outputPanel>

                    <apex:repeat value="{!quoteLineItemToUnitAmountSharedUsageLadderWrappers}" var="wrapper">
                        <!-- 当Product_Cate__c为P1时显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <tr>
                                <td width="20%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="13%" class="account-id-cell">
                                    <apex:outputText value="{!wrapper.formattedAccountId}" escape="false" />
                                </td>
                                <td width="12%">{!wrapper.quoteMethod.Minimum_Amout__c}</td>
                                <td width="12%">{!wrapper.quoteMethod.Minimum_ProdUnitPrice__c}</td>
                                <td width="10%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="16.5%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="16.5%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                            </tr>
                        </apex:outputPanel>
                        <!-- 当Product_Cate__c不为P1时不显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c != 'P1'}">
                            <tr>
                                <td width="25%">{!wrapper.quoteLineItem.Product2.Name}</td>
                                <td width="15%">{!wrapper.quoteMethod.Minimum_Amout__c}</td>
                                <td width="15%">{!wrapper.quoteMethod.Minimum_ProdUnitPrice__c}</td>
                                <td width="12%">{!wrapper.quoteLineItem.Region__c}</td>
                                <td width="16.5%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineStartDate__c}" />
                                    </apex:outputText>
                                </td>
                                <td width="16.5%">
                                    <apex:outputText value="{0, date, yyyy-MM-dd}">
                                        <apex:param value="{!wrapper.quoteLineItem.QuoteLineEndDate__c}" />
                                    </apex:outputText>
                                </td>
                            </tr>
                        </apex:outputPanel>
                    </apex:repeat>
                </table>
            </apex:outputPanel>


            <!-- 为每个报价行项目创建单独的表格 -->
            <!-- <apex:repeat value="{!quoteLineItemWrappers}" var="wrapper">
                <table style="margin-bottom: 5px;">
                    <tr>
                        <td width="12%">{!$Label.Product_Name}</td>
                        <td width="8%">{!$Label.Tax_Rate}</td>
                        <td width="8%">{!$Label.Base_Price}</td>
                        <td width="8%">{!$Label.Customer_AccountID}</td>
                        
                        <apex:variable var="hasAmountCap" value="false" />
                        <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                            <apex:variable var="hasAmountCap" value="{!OR(method.Method__c == '金额封顶', hasAmountCap == 'true')}" />
                        </apex:repeat>
                        <apex:outputPanel layout="none" rendered="{!hasAmountCap == 'true'}">
                            <td width="8%">{!$Label.Cap_Amount}</td>
                        </apex:outputPanel>
                        
                        <apex:variable var="hasFixedDosage" value="false" />
                        <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                            <apex:variable var="hasFixedDosage" value="{!OR(method.Method__c == '固定用量*固定单价', hasFixedDosage == 'true')}" />
                        </apex:repeat>
                        <apex:outputPanel layout="none" rendered="{!hasFixedDosage == 'true'}">
                            <td width="8%">{!$Label.Fixed_Dosage}</td>
                            <td width="8%">{!$Label.Fixed_Unitprice}</td>
                        </apex:outputPanel>
                        
                        <apex:variable var="hasMinimumPrice" value="false" />
                        <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                            <apex:variable var="hasMinimumPrice" value="{!method.Method__c == '保底单价*保底数量' || hasMinimumPrice == 'true'}" />
                        </apex:repeat>
                        <apex:outputPanel layout="none" rendered="{!hasMinimumPrice == 'true'}">
                            <td width="8%">{!$Label.Minimum_Price}</td>
                            <td width="8%">{!$Label.Minimum_Quantity}</td>
                        </apex:outputPanel>
                        
                        <apex:variable var="hasDiscount" value="false" />
                        <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                            <apex:variable var="hasDiscount" value="{!method.Method__c == '产品折扣' || hasDiscount == 'true'}" />
                        </apex:repeat>
                        <apex:outputPanel layout="none" rendered="{!hasDiscount == 'true'}">
                            <td width="8%">{!$Label.Discount_Factor}</td>
                            <td width="8%">{!$Label.Fixed_Rebate}</td>
                            <td width="8%">{!$Label.Cash_Reduction}</td>
                            <td width="8%">{!$Label.Credit}</td>
                        </apex:outputPanel>
                        
                        <td width="10%">{!$Label.Profit_Statement}</td>
                    </tr>
                    
                    <tr>
                        <td>{!wrapper.quoteLineItem.Product2.Name}</td>
                        <td>{!wrapper.quoteLineItem.Tax_Rate__c}</td>
                        <td>{!wrapper.quoteLineItem.UnitPrice}</td>
                        <td>{!wrapper.quoteLineItem.Account_ID__c}</td>
                        
                        <apex:outputPanel layout="none" rendered="{!hasAmountCap == 'true'}">
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Amount_Cap__c}" rendered="{!method.Method__c == '金额封顶'}" />
                                </apex:repeat>
                            </td>
                        </apex:outputPanel>
                        
                        <apex:outputPanel layout="none" rendered="{!hasFixedDosage == 'true'}">
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Fixed_Dosage__c}" rendered="{!method.Method__c == '固定用量*固定单价'}" />
                                </apex:repeat>
                            </td>
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Fixed_UnitPrice__c}" rendered="{!method.Method__c == '固定用量*固定单价'}" />
                                </apex:repeat>
                            </td>
                        </apex:outputPanel>
                        
                        <apex:outputPanel layout="none" rendered="{!hasMinimumPrice == 'true'}">
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Minimum_ProdUnitPrice__c}" rendered="{!method.Method__c == '保底单价*保底数量'}" />
                                </apex:repeat>
                            </td>
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Minimum_Amout__c}" rendered="{!method.Method__c == '保底单价*保底数量'}" />
                                </apex:repeat>
                            </td>
                        </apex:outputPanel>
                        
                        <apex:outputPanel layout="none" rendered="{!hasDiscount == 'true'}">
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Discount_Factor__c}" rendered="{!method.Method__c == '产品折扣'}" />
                                </apex:repeat>
                            </td>
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Fixed_Rebate__c}" rendered="{!method.Method__c == '产品折扣'}" />
                                </apex:repeat>
                            </td>
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Cash_Reduce__c}" rendered="{!method.Method__c == '产品折扣'}" />
                                </apex:repeat>
                            </td>
                            <td>
                                <apex:repeat value="{!wrapper.quoteMethods}" var="method">
                                    <apex:outputText value="{!method.Credit__c}" rendered="{!method.Method__c == '产品折扣'}" />
                                </apex:repeat>
                            </td>
                        </apex:outputPanel>
                        
                        <td>{!wrapper.quoteLineItem.Profit_Statement__c}</td>
                    </tr>
                </table>
            </apex:repeat> -->

            <!-- 如果没有报价行项目，显示一个空表格 -->
            <apex:outputPanel layout="none" rendered="{!AND(quoteLineItemToUnitAmountWrappers.size == 0, quoteLineItemToProductDiscountWrappers.size == 0, quoteLineItemToUsageLadderWrappers.size==0, quoteLineItemToMinAmountSharedAmountDiscountWrappers.size==0, quoteLineItemToUnitAmountSharedUsageLadderWrappers.size==0)}">
                <table> 
                    <tr>
                        <td width="12%">{!$Label.Product_Name}</td>
                        <!-- <td width="8%">{!$Label.Tax_Rate}</td> -->
                        <!-- <td width="8%">{!$Label.Base_Price}</td> -->
                        <!-- 只有当Product_Cate__c为P1时才显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <td width="8%">{!$Label.Customer_AccountID}</td>
                        </apex:outputPanel>
                        <!-- <td width="10%">{!$Label.Profit_Statement}</td>  -->
                    </tr>
                    <tr>
                       <td></td>
                        <!-- <td></td> -->
                        <!-- 只有当Product_Cate__c为P1时才显示客户账号ID列 -->
                        <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                            <td></td>
                        </apex:outputPanel>
                    </tr>
                 </table>
                  <!-- <div style="font-size:20px"  class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                        暂无报价行数据
                  </div> -->
            </apex:outputPanel>
            
            <!-- 2.1 产品组 -->
            <apex:outputPanel layout="block" rendered="{!productGroups.size > 0}">
                <table style="width: 100%;">
                    <tr>
                        <td colspan="10">{!$Label.Service_Group_Content}：</td>
                    </tr>
                    
                    <!-- 产品组循环 -->
                    <apex:repeat value="{!productGroups}" var="group">
                        <tr>
                            <td colspan="10" style="background-color: #f8f8f8; font-weight: bold; padding: 8px;">
                                组合{!group.groupNumber}
                            </td>
                        </tr>
                        
                        <!-- 产品组表头 -->
                        <tr class="header-row">
                            <td width="20%">{!$Label.Product_Name}</td>
                            <!-- <td width="8%">{!$Label.Tax_Rate}</td> -->
                            <!-- <td width="8%">{!$Label.Base_Price}</td> -->
                            <!-- 只有当Product_Cate__c为P1时才显示客户账号ID列 -->
                            <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                                <td width="8%">{!$Label.Customer_AccountID}</td>
                            </apex:outputPanel>
                            <!-- 只在没有单价值时显示牌价列 -->
                            <apex:outputPanel layout="none" rendered="{!group.minimumUnitPrice == null}">
                                <td width="8%">牌价</td>
                            </apex:outputPanel>

                            <!-- 保底金额+共享阶梯金额折扣落区 -->
                            <apex:outputPanel layout="none" rendered="{!group.hasMinAmountSharedLadderAmountDiscountDown}">
                                <td width="10%">{!$Label.Guaranteed_Minimum_Amount}</td>
                                <!-- <td colspan="4">{!$Label.Profit_Statement}</td> -->

                            </apex:outputPanel>

                            <!-- 保底单价*保底数量+共享阶梯用量单价落区 -->
                            <apex:outputPanel layout="none" rendered="{!group.hasMinUnitPriceSharedLadderUsagePriceDown}">
                                <td width="8%">{!$Label.Minimum_Price}</td>
                                <td width="8%">{!$Label.Minimum_Quantity}</td>
                                <!-- <td colspan="3">{!$Label.Profit_Statement}</td> -->

                            </apex:outputPanel>

                            <!-- 阶梯用量 -->
                            <apex:outputPanel layout="none" rendered="{!group.hasUsageLadderQuote}">
                                <!-- <td width="8%">{!$Label.Fixed_Dosage}</td>
                                <td width="8%">{!$Label.Fixed_Unitprice}</td> -->
                                <!-- <td colspan="5">{!$Label.Profit_Statement}</td> -->

                            </apex:outputPanel>

                            <!-- 产品折扣 -->
                            <apex:outputPanel layout="none" rendered="{!group.hasProductDiscountQuote}">
                                <td width="7%">{!$Label.Discount_Factor}</td>
                                <td width="7%">{!$Label.Fixed_Rebate}</td>
                                <td width="7%">{!$Label.Cash_Reduction}</td>
                                <td width="7%">{!$Label.Credit}</td>
                                <!-- <td width="12%">{!$Label.Profit_Statement}</td> -->

                            </apex:outputPanel>

                             <!-- 单价*数量 -->
                            <apex:outputPanel layout="none" rendered="{!group.hasUnitAmountQuote}">
                                <td width="7%">单价</td>
                                <td width="7%">数量</td>
                                <!-- <td colspan="3">{!$Label.Profit_Statement}</td> -->

                            </apex:outputPanel>

                            <!-- 只有在有阶梯数据时才显示备注列 -->
                            <apex:outputPanel layout="none" rendered="{!OR(group.ladderLines.size > 0, group.hasMinAmountSharedLadderAmountDiscountDown, group.hasMinUnitPriceSharedLadderUsagePriceDown, group.hasUsageLadderQuote)}">
                                <td style="max-width: 100%; width: 100%; ">{!$Label.Notes}</td>
                            </apex:outputPanel>
                        </tr>
                        
                        <!-- 产品循环 -->
                        <apex:variable var="rowNum" value="{!0}" />
                        <apex:repeat value="{!group.products}" var="product">
                            <apex:variable var="rowNum" value="{!rowNum + 1}" />
                            <tr>
                                <td>{!product.Product2.Name}</td>
                                <!-- <td>{!product.Tax_Rate__c}</td> -->
                                <!-- <td>{!product.UnitPrice}</td> -->
                                <!-- 只有当Product_Cate__c为P1时才显示客户账号ID -->
                                <apex:outputPanel layout="none" rendered="{!quote.Product_Cate__c == 'P1'}">
                                    <td class="account-id-cell">
                                        <apex:outputText value="{!formatAccountId(product.Account_ID__c)}" escape="false" />
                                    </td>
                                </apex:outputPanel>
                                <!-- 只在没有单价值时显示牌价 -->
                                <apex:outputPanel layout="none" rendered="{!group.minimumUnitPrice == null}">
                                    <td>
                                        <apex:outputPanel rendered="{!group.productPriceMap[product.Product2Id] != null}">
                                            {!group.productPriceMap[product.Product2Id].Amount__c} {!group.productPriceMap[product.Product2Id].CurrencyIsoCode}
                                        </apex:outputPanel>
                                    </td>
                                </apex:outputPanel>
                                
                                <!-- 保底金额+共享阶梯金额折扣落区 -->
                                <apex:outputPanel layout="none" rendered="{!group.hasMinAmountSharedLadderAmountDiscountDown}">
                                    <td>{!group.minimumAmount}</td>
                                    <!-- <td colspan="4">{!product.Profit_Statement__c}</td> -->

                                </apex:outputPanel>
                                
                                <!-- 保底单价*保底数量+共享阶梯用量单价落区 -->
                                <apex:outputPanel layout="none" rendered="{!group.hasMinUnitPriceSharedLadderUsagePriceDown}">
                                    <td>{!group.minimumUnitPrice}</td>
                                    <td>{!group.minimumQuantity}</td>
                                    <!-- <td colspan="3">{!product.Profit_Statement__c}</td> -->

                                </apex:outputPanel>
                                
                                <!-- 固定金额 -->
                                <apex:outputPanel layout="none" rendered="{!group.hasUsageLadderQuote}">
                                    <!-- <td>{!group.fixedUsage}</td>
                                    <td>{!group.fixedAmount}</td> -->
                                    <!-- <td colspan="5">{!product.Profit_Statement__c}</td> -->

                                </apex:outputPanel>
                                
                                <!-- 产品折扣 -->
                                <apex:outputPanel layout="none" rendered="{!group.hasProductDiscountQuote}">
                                    <td>{!group.discountCoefficient}</td>
                                    <td>{!group.fixedRebate}</td>
                                    <td>{!group.cashReduce}</td>
                                    <td>{!group.credit}</td>
                                    <!-- <td>{!product.Profit_Statement__c}</td> -->

                                </apex:outputPanel>
                                <!-- 单价*数量 -->
                                <apex:outputPanel layout="none" rendered="{!group.hasProductDiscountQuote}">
                                    <td>{!group.unitPrice}</td>
                                    <td>{!group.quantity}</td>
                                    <!-- <td colspan="3">{!product.Profit_Statement__c}</td> -->

                                </apex:outputPanel>
                                
                                
                                 <!-- 合并单元格 - 只有在有阶梯数据时才显示备注列 -->
                                 <apex:outputPanel layout="none" rendered="{!AND(rowNum == 1, OR(group.ladderLines.size > 0, group.hasMinAmountSharedLadderAmountDiscountDown, group.hasMinUnitPriceSharedLadderUsagePriceDown, group.hasUsageLadderQuote))}">
                                    <td rowspan="{!group.products.size}" style="vertical-align: top;">
                                        <!-- 显示产品组报价方式类型 -->
                                        <div style="margin-bottom: 8px; font-weight: bold;">
                                            <apex:outputPanel rendered="{!group.hasMinAmountSharedLadderAmountDiscountDown}">
                                                保底金额+共享阶梯金额折扣落区
                                            </apex:outputPanel>
                                            <apex:outputPanel rendered="{!group.hasMinUnitPriceSharedLadderUsagePriceDown}">
                                                保底单价*保底数量+共享阶梯用量单价落区
                                            </apex:outputPanel>
                                            <!-- <apex:outputPanel rendered="{!group.hasFixedAmountQuote}">
                                                固定金额
                                            </apex:outputPanel>
                                            <apex:outputPanel rendered="{!group.hasProductDiscountQuote}">
                                                产品折扣
                                            </apex:outputPanel> -->
                                            <apex:outputPanel rendered="{!group.hasUsageLadderQuote}">
                                                阶梯用量单价
                                            </apex:outputPanel>
                                            <!-- <apex:outputPanel rendered="{!group.hasSharedLadderAmountDiscountDown && !group.hasMinAmountSharedLadderAmountDiscountDown}">
                                                共享阶梯金额折扣落区
                                            </apex:outputPanel>
                                            <apex:outputPanel rendered="{!group.hasSharedLadderUsagePriceDown && !group.hasMinUnitPriceSharedLadderUsagePriceDown}">
                                                共享阶梯用量单价落区
                                            </apex:outputPanel> -->
                                        </div>
                                        
                                        <!-- 显示阶梯报价信息 -->
                                       <apex:outputPanel layout="none" rendered="{!group.ladderLines.size > 0}">
                                            <apex:variable var="ladderCount" value="{!0}" />
                                            <apex:repeat value="{!group.ladderLines}" var="line">    
                                                <apex:variable var="ladderCount" value="{!ladderCount + 1}" />                                            
                                                <div style="margin-bottom: 5px;">
                                                    <apex:outputText value="{!$Label.Ladder_Step}">
                                                        <apex:param value="{!ladderCount}"/>
                                                    </apex:outputText><br/>
                                                    <apex:outputText value="{!$Label.Usage_Range}">
                                                        <apex:param value="{!line.Down_Limit__c}"/>
                                                        <apex:param value="{!line.Up_Limit__c}"/>
                                                        <apex:param value="{!line.Unit__c}"/>
                                                    </apex:outputText><br/>
                                                    <apex:outputText value="{!$Label.Applicable_Discount}">
                                                        <apex:param value="{!line.Discount__c}"/>
                                                    </apex:outputText><br/>
                                                    <apex:outputText value="{!$Label.Calculation_Method}">
                                                        <apex:param value="{!line.Calculation_Method__c}"/>
                                                    </apex:outputText>
                                                </div>
                                            </apex:repeat>
                                        </apex:outputPanel>
                                    </td>
                                </apex:outputPanel>
                            </tr>
                        </apex:repeat>
                    </apex:repeat>
                </table>
            </apex:outputPanel>
            
            <table>
                <tr>
                    <td width="15%">{!$Label.Contract_Start_Date}：</td>
                    <td width="20%">
                        <apex:outputText value="{0,date,yyyy-MM-dd}">
                            <apex:param value="{!quote.StartDate__c}" />
                        </apex:outputText>
                    </td>
                    <td width="15%">{!$Label.Contract_End_Date}：</td>
                    <td width="20%">
                        <apex:outputText value="{0,date,yyyy-MM-dd}">
                            <apex:param value="{!quote.EndDate__c}" />
                        </apex:outputText>
                    </td>
                    <td width="15%">{!$Label.Contract_Duration}：</td>
                    <td width="15%">{!contractDuration}</td>
                </tr>
                <tr>
                    <td colspan="4">{!$Label.Contract_Base_Price}：</td>
                    <td colspan="2">
                        <apex:outputText value="{0, number, #,##0.00} 美元">
                            <apex:param value="{!contractRevenue}" />
                        </apex:outputText>
                    </td>
                </tr>
            </table>
            
            <!-- 3. 支付方式 -->
            <table>
                <tr>
                    <td colspan="2" class="section-number">3.{!$Label.Payment_Method}</td>
                </tr>
                <tr>
                    <td width="30%">{!$Label.Payment_Method_Agreement}：</td>
                    <td width="70%">{!paymentMethod}</td>
                </tr>
                <tr>
                    <td colspan="2">首付款：{!quote.prepayment__c} {!quote.Contract_Cur__c}，其中含押金：{!quote.DepositAmount__c} {!quote.Contract_Cur__c}。{!paymentMethod}{!paymentCyle}支付。</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <div style="font-size: 11px; line-height: 1.4;">
                            <strong>{!$Label.Payment_Method_Description}：</strong><br/>
                       

                    <apex:outputText styleClass="contract-paragraph" value="{!$Label.Contract_Deposit_Terms_Main}" escape="false">
                        <apex:param value="{!quote.DepositAmount__c}"/>
                    </apex:outputText>

                    <apex:outputText styleClass="contract-paragraph" value="{!$Label.Contract_Deposit_Purpose}" escape="false"/>
                    <apex:outputText styleClass="contract-paragraph" value="{!$Label.Contract_Deposit_Breach}" escape="false"/>
                    <apex:outputText styleClass="contract-paragraph" value="{!$Label.Contract_Deposit_Shortage}" escape="false"/>
                    <apex:outputText styleClass="contract-paragraph" value="{!$Label.Contract_Deposit_Refund}" escape="false"/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>{!$Label.Settlement_Exchange_Rate}：</td>
                    <td>{!quote.ExchangeRateIdentification__c}</td>
                </tr>
            </table>
            
           <!-- 4. 付款相关信息 -->
            <table>
                <tr>
                    <td colspan="2" class="section-number">4.{!$Label.Payment_Information}</td>
                    <td width="45%" >{!paymentCyle}</td>
                </tr>
                
                <tr>
                    
                    <td width="45%">{!$Label.Check_Payment}</td>
                    <td width="55%" colspan="2">{!$Label.Check_Payee}</td>
                </tr>
                <tr>
                    <!-- <td width="15%" rowspan="3">{!quote.Payment_Way__c}</td> -->
                    <td width="15%" rowspan="3">银行转账</td>
                    <td width="15%">{!$Label.Company_Name}：</td>
                    <td width="70%">{!quote.MSP_Company_Name__c}</td>
                </tr>
                <tr>
                    
                   
                    <td width="15%">{!$Label.Bank_Name}：</td>
                    <td width="85%">{!bankInfo.Name}</td>
                </tr>
                <tr>
                    
                    
                    <td width="15%">{!$Label.Bank_Account_Number}：</td>
                </tr>
                <tr>
                    <td>{!$Label.Invoice_Issue}</td>
                    <td>{!$Label.Invoice_Title}：</td>
                    <td colspan="1">{!quote.Invoice_Customer_Title__c}</td>
                </tr>
                <!-- <tr>
                    <td>{!$Label.PartyB_Data_Transfer_Address}：</td>
                    <td colspan="2">
                        {!quote.PartyB_Signing_Company__r.Registered_Country_Province__c}
                        {!quote.PartyB_Signing_Company__r.Street__c}
                        {!quote.PartyB_Signing_Company__r.PostalCode__c}
                        {!quote.PartyB_Signing_Company__r.CompanyAddress__c}
                    </td>
                </tr> -->
                <!-- <tr>
                    <td>乙方注册地址：</td>
                    <td colspan="2">
                        {!quote.PartyB_Signing_Company__r.CompanyAddress__c}
                    </td>
                </tr> -->
            </table>
            
            <!-- 5. 双方联系人信息 -->
            <table>
                <tr>
                    <td colspan="7" class="section-number">5.{!$Label.Contact_Information}</td>
                </tr>
                <tr>
                    <td rowspan="3" width="15%">{!$Label.PartyA_Contact_Information}：</td>
                    <td width="15%">{!$Label.Contract_Contact}：</td>
                    <td width="15%">{!quote.Cucstomer_Contract__r.name}</td>
                    <td width="20%">{!$Label.Contact_Phone}：</td>
                    <td>{!quote.Customer_Finance_Contact__r.phone}</td>
                    <td width="10%">{!$Label.Email}：</td>
                    <td width="25%">{!quote.Cucstomer_Contract__r.Email}</td>
                </tr>
                <tr>
                    <td>{!$Label.Tech_Contact}：</td>
                    <td>{!quote.Customer_Tech_Contact__r.name}</td>
                    <td>{!$Label.Contact_Phone}：</td>
                    <td>{!quote.Customer_Finance_Contact__r.phone}</td>
                    <td>{!$Label.Email}：</td>
                    <td>{!quote.Customer_Tech_Contact__r.Email}</td>
                </tr>
                <tr>
                    <td>{!$Label.Finance_Contact}：</td>
                    <td>{!quote.Customer_Finance_Contact__r.name}</td>
                    <td>{!$Label.Contact_Phone}：</td>
                    <td>{!quote.Customer_Finance_Contact__r.phone}</td>
                    <td>{!$Label.Email}：</td>
                    <td>{!quote.Customer_Finance_Contact__r.Email}</td>
                </tr>
                <tr>
                    <td rowspan="1" width="15%">{!$Label.PartyB_Contact_Information}：</td>
                    <td>销售联系人：</td>
                    <td>{!quote.Owner.Name}</td>
                    <td>{!$Label.Contact_Phone}：</td>
                    <td>{!quote.Owner.phone}</td>
                    <td>{!$Label.Email}：</td>
                    <td>{!quote.owner.Email}</td>
                </tr>
               
            </table>
            
            <!-- 6. 特殊事项说明 -->
            <table>
                <tr>
                    <td colspan="4" class="section-number">{!$Label.Special_Items_Description}</td>
                </tr>
                <tr>
                    <td colspan="4">{!$Label.Special_Items_Description_Content}</td>
                </tr>
            </table>
            
            <!-- 签名区域 -->
            <table style="border: 1px dashed #000; margin-top: 20px;">
                <tr>
                    <td width="50%" style="border: 1px dashed #000;">
                        <!-- <div>{!$Label.PartyA}：{!quote.MSP_Company_Name__c}</div> -->
                        <div>{!$Label.PartyA}：{!quote.Account.Name}</div>

                        <div style="margin-top: 10px;">{!$Label.Stamp_The_Contract_Seal}</div>
                        <div style="margin-top: 20px;">{!$Label.Signature_Authorized_Representative}：</div>
                        <div style="margin-top: 10px;">{!$Label.Date}：</div>
                        <div style="margin-top: 10px;">{!$Label.Phone}：</div>
                        <div style="margin-top: 10px;">{!$Label.Fax}：</div>
                    </td>
                    <td width="50%" style="border: 1px dashed #000;">
                        <div>{!$Label.PartB}： {!quote.PartyB_Signing_Company__r.Name}</div>
                        <div style="margin-top: 10px;">{!$Label.UseToPrint_Here}</div>
                        <div style="margin-top: 20px;">{!$Label.Signature_Authorized_Representative}：</div>
                        <div style="margin-top: 10px;">{!$Label.Date}：</div>
                        <div style="margin-top: 10px;">{!$Label.Phone}：</div>
                        <div style="margin-top: 10px;">{!$Label.Fax}：</div>
                    </td>
                </tr>
            </table>

        </body>
    </html>

    
</apex:page>