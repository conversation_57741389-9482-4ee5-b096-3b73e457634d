import { LightningElement,wire,track,api} from 'lwc';
import { CurrentPageReference } from "lightning/navigation";
import { CloseActionScreenEvent } from 'lightning/actions';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { RefreshViewEvent } from 'lightning/refresh'; 
import resetQuoteToDraft  from '@salesforce/apex/QuoteStatusResetController.resetQuoteToDraft';


export default class QuoteCustomerRefuse extends LightningElement {
    @api recordId; 
    isLoading = false; 
    @wire(CurrentPageReference)
        getStateParameters(currentPageReference) {
                console.log(currentPageReference);

            if (currentPageReference) {
            const urlValue = currentPageReference.state.recordId;
            if (urlValue) {
                let str = `${urlValue}`;
                console.log("str");
                console.log(str);
                this.recordId = str;
            }
            }
    }
    
    handleConfirm() {
        this.isLoading = true;

        resetQuoteToDraft({ quoteId: this.recordId })
            .then((result) => {
                this.dispatchEvent(new ShowToastEvent({
                    title: '操作成功',
                    message: result,
                    variant: 'success'
                }));
                this.dispatchEvent(new RefreshViewEvent());
            })
            .catch((error) => {
                this.dispatchEvent(new ShowToastEvent({
                    title: '操作失败',
                    message: error.body?.message || '重置报价状态失败，请联系管理员',
                    variant: 'error',
                    mode: 'sticky'
                }));
            })
            .finally(() => {
                this.isLoading = false;
                this.closeModal();
            });
    }

   
    handleCancel() {
        this.closeModal();
    }
   
    closeModal() {
        this.dispatchEvent(new CloseActionScreenEvent()); 
    }
}