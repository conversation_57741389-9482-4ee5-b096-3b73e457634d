import { LightningElement,wire,track,api} from 'lwc';
import { CurrentPageReference } from "lightning/navigation";
import { CloseActionScreenEvent } from 'lightning/actions';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import resetQuoteToDraft  from '@salesforce/apex/QuoteStatusResetController.resetQuoteToDraft';


export default class QuoteCustomerRefuse extends LightningElement {
    @api recordId; 
    isLoading = false; 
    @wire(CurrentPageReference)
        getStateParameters(currentPageReference) {
                console.log(currentPageReference);

            if (currentPageReference) {
            const urlValue = currentPageReference.state.recordId;
            if (urlValue) {
                let str = `${urlValue}`;
                console.log("str");
                console.log(str);
                this.recordId = str;
            }
            }
    }
    
    handleConfirm() {
        this.isLoading = true;
        console.log('开始重置报价状态，recordId:', this.recordId);

        resetQuoteToDraft({ quoteId: this.recordId })
            .then((result) => {
                console.log('Apex方法调用成功，返回结果:', result);

                // 显示成功消息
                this.dispatchEvent(new ShowToastEvent({
                    title: '操作成功',
                    message: result + ' 页面将自动刷新。',
                    variant: 'success'
                }));

                // 先关闭模态框，然后刷新页面
                this.closeModal();

                // 延迟刷新页面，确保模态框完全关闭
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch((error) => {
                console.error('Apex方法调用失败:', error);
                console.error('错误详情:', JSON.stringify(error));

                let errorMessage = '重置报价状态失败，请联系管理员';
                if (error.body && error.body.message) {
                    errorMessage = error.body.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }

                this.dispatchEvent(new ShowToastEvent({
                    title: '操作失败',
                    message: errorMessage,
                    variant: 'error',
                    mode: 'sticky'
                }));

                // 关闭模态框
                this.closeModal();
            })
            .finally(() => {
                console.log('操作完成，重置loading状态');
                this.isLoading = false;
            });
    }

   
    handleCancel() {
        this.closeModal();
    }
   
    closeModal() {
        this.dispatchEvent(new CloseActionScreenEvent());
    }


}