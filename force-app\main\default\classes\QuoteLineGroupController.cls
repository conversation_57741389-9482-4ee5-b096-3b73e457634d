/**
 * 产品组管理控制器
 * 用于处理产品组数据的保存和获取
 */
public with sharing class QuoteLineGroupController {
    public static Map<String,String> regionMap;

    public QuoteLineGroupController() {
        // 构造函数
    }

    /**
     * 获取产品组数据
     * @param quoteId 报价单ID
     * @return 产品组数据列表
     */
    @AuraEnabled(cacheable=false)
    public static List<Map<String, Object>> getProductGroups(String quoteId) {
        System.debug('获取产品组数据，报价ID: ' + quoteId);
        List<Map<String, Object>> result = new List<Map<String, Object>>();

        try {
            // 首先查询Quote的服务日期作为默认值
            Date quoteStartDate = null;
            Date quoteEndDate = null;
            for (Quote quote : [SELECT Id, StartDate__c, EndDate__c FROM Quote WHERE Id = :quoteId LIMIT 1]) {
                quoteStartDate = quote.StartDate__c;
                quoteEndDate = quote.EndDate__c;
                System.debug('Quote服务开始日: ' + quoteStartDate + ', 服务结束日: ' + quoteEndDate);
            }
            // 获取报价单下的产品组
            List<QuoteLineItem> quoteLineItems = Database.query(
                'SELECT Id, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
                'Product2.Description, Product2.QuantityUnitOfMeasure, UnitPrice,' +
                'Tax_Rate__c, Account_ID__c, Profit_Statement__c, Description, ' +
                'QuoteLineStartDate__c, QuoteLineEndDate__c, toLabel(Region__c), toLabel(IS_OneTimeFee__c), toLabel(Charge_Type__c), ' +
                'Product_Group__c, Product_Group__r.QuotationMethod_Ladder__c, ' +
                'Product_Group__r.QuotationMethod_Ladder__r.Method__c, ISGROUP__c, ' +
                'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
                'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name ' +
                'FROM QuoteLineItem ' +
                'WHERE QuoteId = :quoteId AND Product_Group__c != null AND ISGROUP__c = true ' +
                'ORDER BY CreatedDate ASC'
            );
            
            // 收集所有产品ID，用于查询产品牌价
            Set<Id> productIds = new Set<Id>();
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Product2Id != null) {
                    productIds.add(qli.Product2Id);
                }
            }
            
            // 查询产品牌价对象
            Map<String, Map<String, Object>> productToPriceMap = new Map<String, Map<String, Object>>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c, Amount__c, CurrencyIsoCode,toLabel(Product_Area__c)
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                Map<String, Object> priceInfo = new Map<String, Object>();
                priceInfo.put('amount', price.Amount__c);
                priceInfo.put('currencyCode', price.CurrencyIsoCode);
                productToPriceMap.put(price.Product__c+ price.Product_Area__c, priceInfo);
            }
           
            
            // 查询所有报价方式数据
            Map<Id, Quotation_Method__c> groupQuotationMethods = new Map<Id, Quotation_Method__c>();
            
            // 保底金额+共享阶梯金额折扣落区组合报价方式
            Map<Id, Quotation_Method__c> qmMap = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> qmList = [
                SELECT Id,Product_Group__c,LadderType__c,toLabel(LadderType__c) LadderTypeLabel,ISGROUP__c, Method__c,toLabel(Method__c) MethodLabel,Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c,Minimum_ProdUnitPrice__c, Minimum_Amout__c,GuaranteedMin_Amount__c,MinimumGuarantee_type__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null)
                AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : qmList) {
                qmMap.put(qm.Product_Group__c,qm);
            }



            
            // 获取所有相关的阶梯报价行
            Map<Id, List<Ladder_Line__c>> ladderLinesMap = new Map<Id, List<Ladder_Line__c>>();
            
            if (!quoteLineItems.isEmpty()) {
                for (Ladder_Line__c line : [
                    SELECT Id, Product_Group__c, Up_Limit__c, Down_Limit__c, 
                            Unit__c, Discount__c, Calculation_Method__c
                    FROM Ladder_Line__c
                    WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null)
                    
                ]) {
                    if (!ladderLinesMap.containsKey(line.Product_Group__c)) {
                        ladderLinesMap.put(line.Product_Group__c, new List<Ladder_Line__c>());
                    }
                    ladderLinesMap.get(line.Product_Group__c).add(line);
                }
            }
            
          
            Map<Id, List<QuoteLineItem>> qliMap= new Map<Id, List<QuoteLineItem>>();
            
            for (QuoteLineItem qli : quoteLineItems) {
                // 产品组处理逻辑
                if (!qliMap.containsKey(qli.Product_Group__c)) {
                    qliMap.put(qli.Product_Group__c, new List<QuoteLineItem>());
                }
                qliMap.get(qli.Product_Group__c).add(qli);
            }
            
            // 构建产品组结果
            Integer groupNumber = 1;
            
            // 构建返回结果
            for (Id pgId : qliMap.keySet()) {
                Map<String, Object> productGroup = new Map<String, Object>();
                productGroup.put('id', qmMap.get(pgId).Id);
                productGroup.put('groupNumber', groupNumber++);
                
                // 处理产品
                List<Map<String, Object>> products = new List<Map<String, Object>>();
                
                // 保存产品组ID (Product_Group__c)
                Id productGroupId = null;
                
                for (QuoteLineItem qli : qliMap.get(pgId)) {
                    Map<String, Object> product = new Map<String, Object>();
                    product.put('id', qli.Id);
                    product.put('productId', qli.Product2Id);
                    product.put('productName', qli.Product2.Name);
                    product.put('ProductCode', qli.Product2.ProductCode);
                    product.put('Name', qli.Product2.Name);
                    product.put('Family', qli.Product2.Family);
                    product.put('Description', qli.Description);
                    product.put('ProductDescription', qli.Product2.Description);
                    product.put('QuantityUnitOfMeasure', qli.Product2.QuantityUnitOfMeasure);
                    product.put('unitPrice', qli.UnitPrice);
                    product.put('taxRate', qli.Tax_Rate__c);
                    product.put('customerAccountId', qli.Account_ID__c);
                    product.put('profitDescription', qli.Profit_Statement__c);
                    // 设置服务日期，如果产品没有设置日期则使用Quote的默认日期
                    product.put('QuoteLineStartDate', qli.QuoteLineStartDate__c != null ? qli.QuoteLineStartDate__c : quoteStartDate);
                    product.put('QuoteLineEndDate', qli.QuoteLineEndDate__c != null ? qli.QuoteLineEndDate__c : quoteEndDate);
                    product.put('Region', qli.Region__c);
                    product.put('OneTimeFee', qli.IS_OneTimeFee__c);
                    product.put('chargeType', qli.Charge_Type__c);
                    if (qli.Product2.Level__c == '4') {
                        product.put('level1', qli.Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name);
                        product.put('level2', qli.Product2.ParentProduct__r.ParentProduct__r.Name);
                        product.put('level3', qli.Product2.ParentProduct__r.Name);
                    } else if (qli.Product2.Level__c == '3') {
                        product.put('level1', qli.Product2.ParentProduct__r.ParentProduct__r.Name);
                        product.put('level2', qli.Product2.ParentProduct__r.Name);
                        product.put('level3', qli.Product2.Name);
                    }
                    // 添加Product_Group__c值用于产品组识别
                    product.put('ladderDiscountId', qli.Product_Group__c);
                    
                    // 添加产品牌价信息
                    if (productToPriceMap.containsKey(qli.Product2Id+qli.Region__c)) {
                        Map<String, Object> priceInfo = productToPriceMap.get(qli.Product2Id+qli.Region__c);
                        product.put('listPrice', priceInfo.get('amount'));
                        product.put('listPriceCurrency', priceInfo.get('currencyCode'));
                    }
                    
                    products.add(product);
              
                }
                productGroup.put('products', products);
                
                productGroup.put('ladderDiscountId', pgId);
                System.debug('设置产品组的ladderDiscountId: ' + pgId);
               
                
                // 处理阶梯报价
                List<Map<String, Object>> tiers = new List<Map<String, Object>>();
                String tierUnit = '用量'; // 默认单位
                String calculationMethod = '分区'; // 默认计算方式
                Boolean isSharedLadderAmountDiscountZone = false;
                Boolean isSharedLadderAmountDiscountDown = false;
                Boolean isSharedLadderUsagePriceDown = false;
                
                for (QuoteLineItem qli : qliMap.get(pgId)) {
                    if (qli.Product_Group__c != null && ladderLinesMap.containsKey(qli.Product_Group__c)) {
                        for (Ladder_Line__c line : ladderLinesMap.get(qli.Product_Group__c)) {
                            Map<String, Object> tier = new Map<String, Object>();
                            tier.put('id', line.Id);
                            tier.put('lowerBound', line.Down_Limit__c);
                            tier.put('upperBound', line.Up_Limit__c);
                            tier.put('unit', line.Unit__c);
                            tier.put('discount', line.Discount__c);
                            tier.put('calculationMethod', line.Calculation_Method__c);
                            tiers.add(tier);
                            
                            // 记录单位和计算方式，用于判断阶梯类型
                            tierUnit = line.Unit__c;
                            calculationMethod = line.Calculation_Method__c;
                        }
                        // 我们只需要处理一次阶梯报价
                        break;
                    }
                }
                productGroup.put('tiers', tiers);
                
                // 判断阶梯类型
                if (!tiers.isEmpty()) {
                    if (tierUnit == '金额' && calculationMethod == '分区') {
                        isSharedLadderAmountDiscountZone = true;
                    } else if (tierUnit == '金额' && calculationMethod == '落区') {
                        isSharedLadderAmountDiscountDown = true;
                    } else if (tierUnit == '用量' && calculationMethod == '落区') {
                        isSharedLadderUsagePriceDown = true;
                    }
                }
                
                // 查找保底金额+共享阶梯金额折扣落区组合报价方式
                Boolean hasMinAmountSharedLadderAmountDiscountDown = false;
                String minimumAmount = null;
                String minimumGuaranteeType = null;

                if(qmMap.containsKey(pgId)&& qmMap.get(pgId).Method__c == '4'){
                    hasMinAmountSharedLadderAmountDiscountDown = true;
                    minimumAmount = String.valueOf(qmMap.get(pgId).GuaranteedMin_Amount__c);
                    minimumGuaranteeType = String.valueOf(qmMap.get(pgId).MinimumGuarantee_type__c);
                }
            
                
                // 查找1+3组合报价方式
                Boolean hasMinUnitPriceSharedLadderUsagePriceDown = false;
                String minimumUnitPrice = null;
                String minimumQuantity = null;

                if(qmMap.containsKey(pgId)&& qmMap.get(pgId).Method__c == '5'){
                    hasMinUnitPriceSharedLadderUsagePriceDown = true;
                    minimumUnitPrice = String.valueOf(qmMap.get(pgId).Minimum_ProdUnitPrice__c);
                    minimumQuantity = String.valueOf(qmMap.get(pgId).Minimum_Amout__c);
                }
                
                // 查找产品折扣报价方式
                Boolean hasProductDiscountQuote = false;
                String discountCoefficient = null;
                String fixedRebate = null;
                String cashReduce = null;
                String credit = null;

                if(qmMap.containsKey(pgId)&& qmMap.get(pgId).Method__c == '2'){
                    hasProductDiscountQuote = true;
                    discountCoefficient = String.valueOf(qmMap.get(pgId).Discount_Factor__c);
                    fixedRebate = String.valueOf(qmMap.get(pgId).Fixed_Rebate__c);
                    cashReduce = String.valueOf(qmMap.get(pgId).Cash_Reduce__c);
                    credit = String.valueOf(qmMap.get(pgId).Credit__c);
                }

                // 查找阶梯金额折扣报价方式
                Boolean hasSharedLadderAmountDiscountZone = false;
                if(qmMap.containsKey(pgId)&& qmMap.get(pgId).Method__c == '6'){
                    hasSharedLadderAmountDiscountZone = true;
                }


                // 查找1报价方式（非组合报价方式）
                Boolean hasMinimumUnitPriceQuote = false;
                String singleMinimumUnitPrice = null;
                String singleMinimumQuantity = null;
                if(qmMap.containsKey(pgId)&& qmMap.get(pgId).Method__c == '1'){
                    hasMinimumUnitPriceQuote = true;
                    singleMinimumUnitPrice = String.valueOf(qmMap.get(pgId).Minimum_ProdUnitPrice__c);
                    singleMinimumQuantity = String.valueOf(qmMap.get(pgId).Minimum_Amout__c);
                } 
                
                // 设置阶梯报价方式标志
                productGroup.put('hasSharedLadderAmountDiscountZone', hasSharedLadderAmountDiscountZone);
                productGroup.put('hasSharedLadderAmountDiscountDown', isSharedLadderAmountDiscountDown);
                productGroup.put('hasSharedLadderUsagePriceDown', isSharedLadderUsagePriceDown);
                
                // 设置组合报价方式标志
                productGroup.put('hasMinAmountSharedLadderAmountDiscountDown', hasMinAmountSharedLadderAmountDiscountDown);
                productGroup.put('minimumAmount', minimumAmount);
                productGroup.put('minimumGuaranteeType', minimumGuaranteeType);
                productGroup.put('hasMinUnitPriceSharedLadderUsagePriceDown', hasMinUnitPriceSharedLadderUsagePriceDown);
                productGroup.put('minimumUnitPrice', minimumUnitPrice);
                productGroup.put('minimumQuantity', minimumQuantity);
            
                
                // 设置产品折扣报价方式标志
                productGroup.put('hasProductDiscountQuote', hasProductDiscountQuote);
                productGroup.put('discountCoefficient', discountCoefficient);
                productGroup.put('fixedRebate', fixedRebate);
                productGroup.put('cashReduce', cashReduce);
                productGroup.put('credit', credit);

                // 设置1报价方式标志
                productGroup.put('hasMinimumUnitPriceQuote', hasMinimumUnitPriceQuote);
                // 注意：如果是单独的1报价方式，使用单独的字段值；如果是组合报价方式，使用组合的字段值
                if (hasMinimumUnitPriceQuote && !hasMinUnitPriceSharedLadderUsagePriceDown) {
                    productGroup.put('minimumUnitPrice', singleMinimumUnitPrice);
                    productGroup.put('minimumQuantity', singleMinimumQuantity);
                }


                
                // 整体报价方式标志
                productGroup.put('hasLadderQuote', !tiers.isEmpty());
                productGroup.put('hasAnyQuoteType',
                              !tiers.isEmpty() ||
                              hasMinAmountSharedLadderAmountDiscountDown ||
                              hasMinUnitPriceSharedLadderUsagePriceDown ||
                              // hasFixedAmountQuote ||  // 注释掉固定金额报价方式
                                hasProductDiscountQuote ||
                                hasMinimumUnitPriceQuote);

                // 设置报价方式名称
                String quoteTypeValue = null;
                if (hasMinAmountSharedLadderAmountDiscountDown) {
                    quoteTypeValue = '5';
                } else if (hasMinUnitPriceSharedLadderUsagePriceDown) {
                    quoteTypeValue = '6';
                } else if (hasProductDiscountQuote) {
                    quoteTypeValue = '2';
                } else if (hasMinimumUnitPriceQuote) {
                    quoteTypeValue = '1';
                } else if (isSharedLadderAmountDiscountZone) {
                    quoteTypeValue = '共享阶梯金额折扣，分区计算';
                } else if (isSharedLadderAmountDiscountDown) {
                    quoteTypeValue = '共享阶梯金额折扣，落区计算';
                } else if (isSharedLadderUsagePriceDown) {
                    quoteTypeValue = '3';
                }
                productGroup.put('quoteTypeValue', quoteTypeValue);
                productGroup.put('quoteTypeLabel', qmMap.get(pgId).get('MethodLabel'));
                productGroup.put('ladderType', qmMap.get(pgId).LadderType__c);
                productGroup.put('ladderTypeLabel', qmMap.get(pgId).get('LadderTypeLabel'));


                result.add(productGroup);
            }
            
            return result;
        } catch (Exception e) {
            result = new List<Map<String, Object>>();
            result.add(new Map<String, Object>{'error message' => e.getMessage()+';行数：'+e.getLineNumber()});
            System.debug(LoggingLevel.ERROR, '获取产品组数据时发生异常: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            // throw e;
            return result;
        }
    }
    
    
    /**
     * 处理从productInfo组件带过来的产品数据 - 临时添加版本（不立即保存到数据库）
     * @param productsJSON 产品数据JSON
     * @param quoteId 报价单ID
     * @param productGroupId 产品组ID，如果为空则创建新的产品组，如果为'single'则表示添加单个产品
     * @return 操作结果消息和产品数据
     */
    @AuraEnabled(cacheable=false)
    public static Map<String, Object> handleProductInfoProductsTemp(String productsJSON, String quoteId, String productGroupId) {
        System.debug('临时处理productInfo带来的产品数据，报价ID: ' + quoteId + ', 产品组ID: ' + productGroupId);
        System.debug('接收到的JSON数据: ' + productsJSON);

        Map<String, Object> result = new Map<String, Object>();

        try {
            // 解析JSON数据
            List<Object> productsList = (List<Object>) JSON.deserializeUntyped(productsJSON);
            System.debug('解析后的产品数量: ' + productsList.size());

            // 判断是否是添加单个产品
            Boolean isSingleProduct = (productGroupId == 'single');

            // 首先查询报价单的价格手册ID和服务日期，以及汇率差信息
            Id pricebookId = null;
            String currencyCode='';
            Date quoteStartDate = null;
            Date quoteEndDate = null;
            Boolean hasExchangeRateDifference = false;
            Decimal baseUnitPrice = 1.0; // 默认基准价

            for (Quote quote : [SELECT Id, Pricebook2Id, StartDate__c, EndDate__c, CurrencyIsoCode, Contract_Cur__c, Settlement_Cur__c FROM Quote WHERE Id = :quoteId LIMIT 1]) {
                pricebookId = quote.Pricebook2Id;
                quoteStartDate = quote.StartDate__c;
                quoteEndDate = quote.EndDate__c;
                currencyCode = quote.CurrencyIsoCode;

                // 判断是否存在汇率差
                hasExchangeRateDifference = (quote.Contract_Cur__c != quote.Settlement_Cur__c);

                // 根据汇率差设置基准价
                if (hasExchangeRateDifference) {
                    baseUnitPrice = 1.02; // 有汇率差时设置为1.02
                } else {
                    baseUnitPrice = 1.0;   // 没有汇率差时设置为1
                }

                System.debug('报价单价格手册ID: ' + pricebookId);
                System.debug('报价单服务开始日: ' + quoteStartDate);
                System.debug('报价单服务结束日: ' + quoteEndDate);
                System.debug('是否存在汇率差: ' + hasExchangeRateDifference);
                System.debug('基准价设置为: ' + baseUnitPrice);
            }

            // 如果报价单没有价格手册，返回错误信息
            if (pricebookId == null) {
                System.debug('错误: 报价单没有指定价格手册');
                result.put('success', false);
                result.put('message', '添加产品失败: 报价单未指定价格手册，请先为报价单设置价格手册');
                return result;
            }

            // 查询价格表条目 - 确保使用报价单的价格手册ID
            Map<Id, Id> productToPbEntryMap = new Map<Id, Id>();
            for (PricebookEntry pbe : [
                SELECT Id, Product2Id
                FROM PricebookEntry
                WHERE Pricebook2Id = :pricebookId
                AND IsActive = true
                AND CurrencyIsoCode =:currencyCode
            ]) {
                productToPbEntryMap.put(pbe.Product2Id, pbe.Id);
            }

            System.debug('查询到价格手册条目数量: ' + productToPbEntryMap.size());

            // 收集所有产品ID，用于查询产品牌价
            Set<Id> productIds = new Set<Id>();
            for (Object productObj : productsList) {
                if (!(productObj instanceof Map<String, Object>)) {
                    continue;
                }

                Map<String, Object> product = (Map<String, Object>)productObj;

                if (product.containsKey('productId') && product.get('productId') != null) {
                    String productId = String.valueOf(product.get('productId'));
                    if (String.isNotBlank(productId)) {
                        productIds.add(Id.valueOf(productId));
                    }
                }
            }

            // 查询产品牌价对象，获取牌价Amount__c和币种CurrencyIsoCode
            Map<Id, Map<String, Object>> productToPriceMap = new Map<Id, Map<String, Object>>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                Map<String, Object> priceInfo = new Map<String, Object>();
                priceInfo.put('amount', price.Amount__c);
                priceInfo.put('currencyCode', price.CurrencyIsoCode);
                productToPriceMap.put(price.Product__c, priceInfo);
            }

            // 查询产品详细信息
            Map<Id, Product2> productDetailsMap = new Map<Id, Product2>([
                SELECT Id, Name, ProductCode, Family, Description, QuantityUnitOfMeasure,
                       Level__c, ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Name,
                       ParentProduct__r.ParentProduct__r.ParentProduct__r.Name
                FROM Product2
                WHERE Id IN :productIds
            ]);

            // 处理产品数据，构建返回的产品列表
            List<Map<String, Object>> processedProducts = new List<Map<String, Object>>();
            List<String> missingProductIds = new List<String>(); // 记录找不到价格条目的产品

            for (Object productObj : productsList) {
                if (!(productObj instanceof Map<String, Object>)) {
                    continue;
                }

                Map<String, Object> product = (Map<String, Object>)productObj;

                // 检查必要的字段
                if (!product.containsKey('productId') || product.get('productId') == null) {
                    continue;
                }

                String productId = String.valueOf(product.get('productId'));
                if (String.isBlank(productId)) {
                    continue;
                }

                if (!productToPbEntryMap.containsKey(Id.valueOf(productId))) {
                    System.debug('未找到产品的价格表条目: ' + productId);
                    missingProductIds.add(productId);
                    continue;
                }

                // 构建产品数据对象
                Map<String, Object> processedProduct = new Map<String, Object>();
                processedProduct.put('id', 'temp_product_' + DateTime.now().getTime() + '_' + Math.random());
                processedProduct.put('productId', productId);
                processedProduct.put('PricebookEntryId', productToPbEntryMap.get(Id.valueOf(productId)));
                processedProduct.put('ISGROUP__c', !isSingleProduct);

                // 添加产品详细信息
                if (productDetailsMap.containsKey(Id.valueOf(productId))) {
                    Product2 prod = productDetailsMap.get(Id.valueOf(productId));
                    processedProduct.put('Name', prod.Name);
                    processedProduct.put('ProductCode', prod.ProductCode);
                    processedProduct.put('Family', prod.Family);
                    processedProduct.put('ProductDescription', prod.Description);
                    processedProduct.put('QuantityUnitOfMeasure', prod.QuantityUnitOfMeasure);

                    // 处理产品层级信息
                    if (prod.Level__c == '4') {
                        processedProduct.put('level1', prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name);
                        processedProduct.put('level2', prod.ParentProduct__r.ParentProduct__r.Name);
                        processedProduct.put('level3', prod.ParentProduct__r.Name);
                    } else if (prod.Level__c == '3') {
                        processedProduct.put('level1', prod.ParentProduct__r.ParentProduct__r.Name);
                        processedProduct.put('level2', prod.ParentProduct__r.Name);
                        processedProduct.put('level3', prod.Name);
                    }
                }

                // 设置基准价 - 根据汇率差自动设置，不使用前端传入的值
                processedProduct.put('unitPrice', baseUnitPrice);
                // 设置其他字段
                if (product.containsKey('listPrice')) {
                    String listPriceStr = String.valueOf(product.get('listPrice'));
                    if (String.isNotBlank(listPriceStr)) {
                        processedProduct.put('listPrice', Decimal.valueOf(listPriceStr));
                    }
                }

                if (product.containsKey('taxRate')) {
                    String taxRateStr = String.valueOf(product.get('taxRate'));
                    if (String.isNotBlank(taxRateStr)) {
                        processedProduct.put('taxRate', Decimal.valueOf(taxRateStr));
                    }
                }

                if (product.containsKey('customerAccountId')) {
                    processedProduct.put('customerAccountId', String.valueOf(product.get('customerAccountId')));
                }

                if (product.containsKey('profitDescription')) {
                    processedProduct.put('profitDescription', String.valueOf(product.get('profitDescription')));
                }

                if (product.containsKey('Description')) {
                    processedProduct.put('Description', String.valueOf(product.get('Description')));
                }

                // 处理服务开始日和服务截止日 - 如果产品没有设置日期，使用Quote的默认日期
                if (product.containsKey('QuoteLineStartDate')) {
                    String startDateStr = String.valueOf(product.get('QuoteLineStartDate'));
                    if (String.isNotBlank(startDateStr) && startDateStr != 'null') {
                        processedProduct.put('QuoteLineStartDate', startDateStr);
                    } else if (quoteStartDate != null) {
                        processedProduct.put('QuoteLineStartDate', String.valueOf(quoteStartDate));
                    }
                } else if (quoteStartDate != null) {
                    processedProduct.put('QuoteLineStartDate', String.valueOf(quoteStartDate));
                }

                if (product.containsKey('QuoteLineEndDate')) {
                    String endDateStr = String.valueOf(product.get('QuoteLineEndDate'));
                    if (String.isNotBlank(endDateStr) && endDateStr != 'null') {
                        processedProduct.put('QuoteLineEndDate', endDateStr);
                    } else if (quoteEndDate != null) {
                        processedProduct.put('QuoteLineEndDate', String.valueOf(quoteEndDate));
                    }
                } else if (quoteEndDate != null) {
                    processedProduct.put('QuoteLineEndDate', String.valueOf(quoteEndDate));
                }

                // 处理区域字段
                if (product.containsKey('Region')) {
                    processedProduct.put('Region', String.valueOf(product.get('Region')));
                }

                // 处理是否一次性费用字段
                if (product.containsKey('OneTimeFee')) {
                    String oneTimeFeeValue = String.valueOf(product.get('OneTimeFee'));
                    processedProduct.put('OneTimeFee', String.isNotBlank(oneTimeFeeValue) ? oneTimeFeeValue : '否');
                } else {
                    processedProduct.put('OneTimeFee', '否');
                }

                // 处理计费类型字段
                if (product.containsKey('chargeType')) {
                    String chargeTypeValue = String.valueOf(product.get('chargeType'));
                    processedProduct.put('chargeType', String.isNotBlank(chargeTypeValue) ? chargeTypeValue : '计费');
                } else {
                    processedProduct.put('chargeType', '计费');
                }

                // 添加产品牌价信息
                Id prod2Id = Id.valueOf(productId);
                if (productToPriceMap.containsKey(prod2Id)) {
                    Map<String, Object> priceInfo = productToPriceMap.get(prod2Id);
                    processedProduct.put('listPrice', priceInfo.get('amount'));
                    processedProduct.put('listPriceCurrency', priceInfo.get('currencyCode'));
                }

                processedProducts.add(processedProduct);
            }

            // 检查是否有找不到价格条目的产品
            if (!missingProductIds.isEmpty()) {
                result.put('success', false);
                result.put('message', '添加产品失败: 以下产品未在报价单指定的价格手册中找到价格条目: ' + String.join(missingProductIds, ', ') + '。请确保产品已添加到报价单的价格手册中。');
                return result;
            }

            // 返回成功结果
            result.put('success', true);
            result.put('products', processedProducts);
            result.put('isSingleProduct', isSingleProduct);

            if (isSingleProduct) {
                result.put('message', '成功准备单个产品：已准备' + processedProducts.size() + '个产品');
            } else {
                result.put('message', '成功准备产品组产品：已准备' + processedProducts.size() + '个产品');
            }

            return result;

        } catch (Exception e) {
            String errorMsg = '错误: ' + e.getMessage() + ' 位置: ' + e.getLineNumber();
            System.debug(LoggingLevel.ERROR, '临时处理productInfo带来的产品数据时发生异常: ' + errorMsg);
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            result.put('success', false);
            result.put('message', errorMsg);
            return result;
        }
    }

    /**
     * 处理从productInfo组件带过来的产品数据
     * @param productsJSON 产品数据JSON
     * @param quoteId 报价单ID
     * @param productGroupId 产品组ID，如果为空则创建新的产品组，如果为'single'则表示添加单个产品
     * @return 操作结果消息
     */
    @AuraEnabled(cacheable=false)
    public static String handleProductInfoProducts(String productsJSON, String quoteId, String productGroupId) {
        System.debug('处理productInfo带来的产品数据，报价ID: ' + quoteId + ', 产品组ID: ' + productGroupId);
        System.debug('接收到的JSON数据: ' + productsJSON);
        
        try {
            // 解析JSON数据
            List<Object> productsList = (List<Object>) JSON.deserializeUntyped(productsJSON);
            System.debug('解析后的产品数量: ' + productsList.size());
            
            // 判断是否是添加单个产品
            Boolean isSingleProduct = (productGroupId == 'single');
              // 声明产品组变量，但只在非单产品模式下初始化
                Product_Group__c targetProductGroup;
            
            // 如果不是单个产品，则处理产品组逻辑
            if (!isSingleProduct) {
               
                
                if (String.isNotBlank(productGroupId)) {
                    // 如果提供了产品组ID，尝试查询现有产品组
                    List<Product_Group__c> productGroups = [
                        SELECT Id, QuotationMethod_Ladder__c
                        FROM Product_Group__c
                        WHERE Id = :productGroupId
                    ];
                    if (!productGroups.isEmpty()) {
                        targetProductGroup = productGroups[0];
                        System.debug('找到现有产品组: ' + targetProductGroup.Id);
                    }
                }
                
                // 如果没有找到现有产品组，创建新的产品组
                if (targetProductGroup == null) {
                    System.debug('创建新的产品组');
                    
                    // 创建新的报价方式
                    Quotation_Method__c quotationMethod = new Quotation_Method__c(
                        Method__c = '5'  // 使用有效的选择列表值
                    );
                    insert quotationMethod;
                    System.debug('创建新的报价方式: ' + quotationMethod.Id);
                    
                    // 创建新的产品组
                    targetProductGroup = new Product_Group__c(
                        QuotationMethod_Ladder__c = quotationMethod.Id
                    );
                    insert targetProductGroup;
                    System.debug('创建新的产品组: ' + targetProductGroup.Id);
                }
            }
            
            // 首先查询报价单的价格手册ID和服务日期
            Id pricebookId = null;
            Date quoteStartDate = null;
            Date quoteEndDate = null;
            for (Quote quote : [SELECT Id, Pricebook2Id, StartDate__c, EndDate__c FROM Quote WHERE Id = :quoteId LIMIT 1]) {
                pricebookId = quote.Pricebook2Id;
                quoteStartDate = quote.StartDate__c;
                quoteEndDate = quote.EndDate__c;
                System.debug('报价单价格手册ID: ' + pricebookId);
                System.debug('报价单服务开始日: ' + quoteStartDate);
                System.debug('报价单服务结束日: ' + quoteEndDate);
            }
            
            // 如果报价单没有价格手册，返回错误信息
            if (pricebookId == null) {
                System.debug('错误: 报价单没有指定价格手册');
                return '添加产品失败: 报价单未指定价格手册，请先为报价单设置价格手册';
            }
            
            // 查询价格表条目 - 确保使用报价单的价格手册ID
            Map<Id, Id> productToPbEntryMap = new Map<Id, Id>();
            for (PricebookEntry pbe : [
                SELECT Id, Product2Id 
                FROM PricebookEntry 
                WHERE Pricebook2Id = :pricebookId
                AND IsActive = true
            ]) {
                productToPbEntryMap.put(pbe.Product2Id, pbe.Id);
            }
            
            System.debug('查询到价格手册条目数量: ' + productToPbEntryMap.size());
            
            // 收集所有产品ID，用于查询产品牌价
            Set<Id> productIds = new Set<Id>();
            for (Object productObj : productsList) {
                if (!(productObj instanceof Map<String, Object>)) {
                    continue;
                }
                
                Map<String, Object> product = (Map<String, Object>)productObj;
                
                if (product.containsKey('productId') && product.get('productId') != null) {
                    String productId = String.valueOf(product.get('productId'));
                    if (String.isNotBlank(productId)) {
                        productIds.add(Id.valueOf(productId));
                    }
                }
            }
            
            // 查询产品牌价对象，获取牌价Amount__c和币种CurrencyIsoCode
            Map<Id, Map<String, Object>> productToPriceMap = new Map<Id, Map<String, Object>>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                Map<String, Object> priceInfo = new Map<String, Object>();
                priceInfo.put('amount', price.Amount__c);
                priceInfo.put('currencyCode', price.CurrencyIsoCode);
                productToPriceMap.put(price.Product__c, priceInfo);
            }
            
            // 创建要插入的产品行项目
            List<QuoteLineItem> quoteLineItemsToInsert = new List<QuoteLineItem>();
            List<String> missingProductIds = new List<String>(); // 记录找不到价格条目的产品
            
            for (Object productObj : productsList) {
                if (!(productObj instanceof Map<String, Object>)) {
                    continue;
                }
                
                Map<String, Object> product = (Map<String, Object>)productObj;
                
                // 检查必要的字段
                if (!product.containsKey('productId') || product.get('productId') == null) {
                    continue;
                }
                
                String productId = String.valueOf(product.get('productId'));
                if (String.isBlank(productId)) {
                    continue;
                }
                
                if (!productToPbEntryMap.containsKey(Id.valueOf(productId))) {
                    System.debug('未找到产品的价格表条目: ' + productId);
                    missingProductIds.add(productId);
                    continue;
                }
                
                // 创建新的报价行项目，根据isSingleProduct设置ISGROUP__c
                QuoteLineItem quoteLineItem = new QuoteLineItem(
                    QuoteId = quoteId,
                    Product2Id = productId,
                    PricebookEntryId = productToPbEntryMap.get(Id.valueOf(productId)),
                    Quantity = 1,
                    ISGROUP__c = !isSingleProduct,  // 如果是单个产品则设置为false，否则为true
                    Description = ''  // 设置默认备注为空字符串
                );
                
                // 只有产品组的产品才设置Product_Group__c关联
                 if (!isSingleProduct && targetProductGroup != null) {
                    quoteLineItem.Product_Group__c = targetProductGroup.Id;
                }
                
                // 设置其他字段
                if (product.containsKey('unitPrice')) {
                    String unitPriceStr = String.valueOf(product.get('unitPrice'));
                    if (String.isNotBlank(unitPriceStr)) {
                        quoteLineItem.UnitPrice = Decimal.valueOf(unitPriceStr);
                    }
                }
                // 设置其他字段
                if (product.containsKey('listPrice')) {
                    String listPriceStr = String.valueOf(product.get('listPrice'));
                    if (String.isNotBlank(listPriceStr)) {
                        quoteLineItem.ListPrice__c = Decimal.valueOf(listPriceStr);
                    }
                }
                
                if (product.containsKey('taxRate')) {
                    String taxRateStr = String.valueOf(product.get('taxRate'));
                    if (String.isNotBlank(taxRateStr)) {
                        quoteLineItem.Tax_Rate__c = Decimal.valueOf(taxRateStr);
                    }
                }
                
                if (product.containsKey('customerAccountId')) {
                    String customerIdStr = String.valueOf(product.get('customerAccountId'));
                    quoteLineItem.Account_ID__c = customerIdStr;
                }
                
                if (product.containsKey('profitDescription')) {
                    String profitDescStr = String.valueOf(product.get('profitDescription'));
                    quoteLineItem.Profit_Statement__c = profitDescStr;
                }
                
                // 处理备注字段
                if (product.containsKey('Description')) {
                    String descriptionStr = String.valueOf(product.get('Description'));
                    quoteLineItem.Description = descriptionStr;
                }
                
                // 处理服务开始日和服务截止日 - 如果产品没有设置日期，使用Quote的默认日期
                if (product.containsKey('QuoteLineStartDate')) {
                    String startDateStr = String.valueOf(product.get('QuoteLineStartDate'));
                    if (String.isNotBlank(startDateStr) && startDateStr != 'null') {
                        try {
                            quoteLineItem.QuoteLineStartDate__c = Date.valueOf(startDateStr);
                        } catch (Exception e) {
                            System.debug('无效的服务开始日格式: ' + startDateStr);
                        }
                    } else if (quoteStartDate != null) {
                        quoteLineItem.QuoteLineStartDate__c = quoteStartDate;
                    }
                } else if (quoteStartDate != null) {
                    quoteLineItem.QuoteLineStartDate__c = quoteStartDate;
                }

                if (product.containsKey('QuoteLineEndDate')) {
                    String endDateStr = String.valueOf(product.get('QuoteLineEndDate'));
                    if (String.isNotBlank(endDateStr) && endDateStr != 'null') {
                        try {
                            quoteLineItem.QuoteLineEndDate__c = Date.valueOf(endDateStr);
                        } catch (Exception e) {
                            System.debug('无效的服务截止日格式: ' + endDateStr);
                        }
                    } else if (quoteEndDate != null) {
                        quoteLineItem.QuoteLineEndDate__c = quoteEndDate;
                    }
                } else if (quoteEndDate != null) {
                    quoteLineItem.QuoteLineEndDate__c = quoteEndDate;
                }
               
                // 处理区域字段
                if (product.containsKey('Region')) {
                    if (regionMap == null) { // 仅初始化一次，避免重复查询
                        // 调用getPicklistValues获取QuoteLineItem对象的Region__c字段的Picklist映射（标签→API值）
                        regionMap = getPicklistValues('QuoteLineItem', 'Region__c');
                        System.debug('动态初始化区域映射regionMap: ' + regionMap);
                    }
                    String regionStr = String.valueOf(product.get('Region'));
                    quoteLineItem.Region__c = regionMap.get(regionStr);
                }

                // 处理是否一次性费用字段
                if (product.containsKey('OneTimeFee')) {
                    String oneTimeFeeStr = String.valueOf(product.get('OneTimeFee'));
                    quoteLineItem.IS_OneTimeFee__c = String.isNotBlank(oneTimeFeeStr) ? oneTimeFeeStr : '否';
                } else {
                    quoteLineItem.IS_OneTimeFee__c = '否';
                }

                // 处理计费类型字段
                if (product.containsKey('chargeType')) {
                    String chargeTypeStr = String.valueOf(product.get('chargeType'));
                    quoteLineItem.Charge_Type__c = mapChargeTypeLabelToApiName(chargeTypeStr);
                } else {
                    quoteLineItem.Charge_Type__c = '2'; // 默认为计费
                }

                // 处理计费项说明字段
                if (product.containsKey('ChargeExplanation')) {
                    String chargeExplanationStr = String.valueOf(product.get('ChargeExplanation'));
                    quoteLineItem.Charge_Explanation__c = String.isNotBlank(chargeExplanationStr) && !chargeExplanationStr.equals('null') ? chargeExplanationStr : null;
                }
                
                // 添加产品牌价信息
                Id prod2Id = Id.valueOf(productId);
                if (productToPriceMap.containsKey(prod2Id)) {
                    Map<String, Object> priceInfo = productToPriceMap.get(prod2Id);
                    // 记录产品牌价信息，但不设置到QuoteLineItem对象中
                    // 因为QuoteLineItem对象中没有对应的字段
                    System.debug('产品[' + productId + ']的牌价信息: 金额=' + 
                               priceInfo.get('amount') + ', 币种=' + 
                               priceInfo.get('currencyCode'));
                }
                
                quoteLineItemsToInsert.add(quoteLineItem);
            }
            
            // 检查是否有找不到价格条目的产品
            if (!missingProductIds.isEmpty()) {
                return '添加产品失败: 以下产品未在报价单指定的价格手册中找到价格条目: ' + String.join(missingProductIds, ', ') + '。请确保产品已添加到报价单的价格手册中。';
            }
            
            // 插入报价行项目
            if (!quoteLineItemsToInsert.isEmpty()) {
                try {
                    insert quoteLineItemsToInsert;
                    System.debug('成功插入QuoteLineItems: ' + quoteLineItemsToInsert.size() + '条');

                    // 更新Quote对象的利润率字段
                    // updateQuoteProfitRate(quoteId);

                    if (isSingleProduct) {
                        return '成功添加单个产品：已添加' + quoteLineItemsToInsert.size() + '个产品';
                    } else if (targetProductGroup != null) {
                        return '成功添加产品到产品组：已添加' + quoteLineItemsToInsert.size() + '个产品到产品组ID: ' + targetProductGroup.Id;
                    } else {
                        return '成功添加产品到产品组：已添加' + quoteLineItemsToInsert.size() + '个产品';
                    }
                } catch (Exception e) {
                    System.debug(LoggingLevel.ERROR, '插入QuoteLineItems时发生异常: ' + e.getMessage());
                    System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
                    return '添加产品失败: ' + e.getMessage() + '。请确保报价单已指定正确的价格手册，且所选产品在该价格手册中存在。';
                }
            } else {
                return '没有有效的产品数据可以添加';
            }
        } catch (Exception e) {
            String errorMsg = '错误: ' + e.getMessage() + ' 位置: ' + e.getLineNumber();
            System.debug(LoggingLevel.ERROR, '处理productInfo带来的产品数据时发生异常: ' + errorMsg);
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            return errorMsg;
        }
    }


    /**
     * 保存产品组报价数据
     * @param productGroupsJSON 产品组报价JSON数据
     * @param quoteId 报价单ID
     * @return 操作结果消息
     */
    @AuraEnabled(cacheable=false)
    public static Map<String,String> saveProductGroupQuote(String productGroupsJSON, String quoteId) {
        Map<String,String> returnMsg = new Map<String,String>();
        returnMsg.put('isSuccess', 'false');
        returnMsg.put('msg', '');
        Savepoint spoint = Database.setSavepoint();

        System.debug('保存产品组数据，报价ID: ' + quoteId);
        System.debug('接收到的产品组JSON数据: ' + productGroupsJSON);
        
        String result = '';
        
        try {
            // 解析JSON数据
            List<Object> productGroupsList = (List<Object>) JSON.deserializeUntyped(productGroupsJSON);
            System.debug('解析后的产品组数量: ' + productGroupsList.size());
            
            // 查询现有的报价方式ID
            List<Quotation_Method__c> deleteQMList = [
                SELECT Id, Quote_Line_Item_ID__c,LadderType__c, Method__c, ISGROUP__c,
                       Fixed_Dosage__c, Fixed_UnitPrice__c, Discount_Factor__c, Fixed_Rebate__c,
                       Cash_Reduce__c, Credit__c, GuaranteedMin_Amount__c, Minimum_ProdUnitPrice__c, Minimum_Amout__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null)
                AND ISGROUP__c = true
            ];
            List<Ladder_Line__c> deleteLLList= [SELECT Id, QuoteLineItem__c, Down_Limit__c, Up_Limit__c, Unit__c, Discount__c, Calculation_Method__c
                                                    FROM Ladder_Line__c
                                                    WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null)];
  
            List<QuoteLineItem> deleteQLIList =[
                SELECT Id, Product2Id, UnitPrice, Tax_Rate__c, Account_ID__c, Profit_Statement__c,
                       Product_Group__c, Product_Group__r.QuotationMethod_Ladder__c, ISGROUP__c
                FROM QuoteLineItem
                WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null];
            List<Product_Group__c> deletePGIList =[
                SELECT Id FROM Product_Group__c
                WHERE Id IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true AND Product_Group__c != null)];

            if(deleteQMList.size()>0){
                delete deleteQMList;
            }
            if(deleteLLList.size()>0){
                delete deleteLLList;
            }
            if(deleteQLIList.size()>0){
                delete deleteQLIList;
            }
            if(deletePGIList.size()>0){
                delete deletePGIList;
            }

            // 获取报价单的价格手册 Id 和汇率差信息
            Id quotePricebookId;
            String currencyCode;
            Boolean hasExchangeRateDifference = false;
            Decimal baseUnitPrice = 1.0; // 默认基准价

            try {
                Quote quote = [SELECT Pricebook2Id, CurrencyIsoCode, Contract_Cur__c, Settlement_Cur__c FROM Quote WHERE Id = :quoteId LIMIT 1];
                quotePricebookId = quote.Pricebook2Id;
                currencyCode = quote.CurrencyIsoCode;

                // 判断是否存在汇率差
                hasExchangeRateDifference = (quote.Contract_Cur__c != quote.Settlement_Cur__c);

                // 根据汇率差设置基准价
                if (hasExchangeRateDifference) {
                    baseUnitPrice = 1.02; // 有汇率差时设置为1.02
                } else {
                    baseUnitPrice = 1.0;   // 没有汇率差时设置为1
                }

                System.debug('是否存在汇率差: ' + hasExchangeRateDifference);
                System.debug('基准价设置为: ' + baseUnitPrice);

            } catch(Exception ex){
                throw new AuraHandledException('无法获取报价单的价格手册信息: '+ex.getMessage());
            }
           
            // 查询价格表条目
            Map<Id, Id> productToPbEntryMap = new Map<Id, Id>();
            for (PricebookEntry pbe : [
                SELECT Id, Product2Id 
                FROM PricebookEntry 
                WHERE Pricebook2.IsStandard = true
                AND Pricebook2Id = :quotePricebookId
                AND IsActive = true 
                AND CurrencyIsoCode =:currencyCode
            ]) {
                productToPbEntryMap.put(pbe.Product2Id, pbe.Id);
            }


            
            Map<Integer,Product_Group__c> insertPGMap = new Map<Integer,Product_Group__c>();
            List<Quotation_Method__c> insertQMList = new List<Quotation_Method__c>();
            List<Ladder_Line__c> insertLLList = new List<Ladder_Line__c>();
            List<QuoteLineItem> insertQLIList = new List<QuoteLineItem>();

            Integer groupIndex = 1;
            for (Object groupObj : productGroupsList) {
                if (!(groupObj instanceof Map<String, Object>)) {
                    continue;
                }

                // 为每个产品组创建或找到一个共享的Product_Group__c
                Product_Group__c productGroup = new Product_Group__c();
                insertPGMap.put(groupIndex,productGroup);
                groupIndex++;

            }
            if(insertPGMap.size()>0){
                insert insertPGMap.values();
            }
       
            groupIndex = 1;
            for (Object groupObj : productGroupsList) {
                if (!(groupObj instanceof Map<String, Object>)) {
                    continue;
                }

                Map<String, Object> productGroup = (Map<String, Object>)groupObj;
                Product_Group__c pg = insertPGMap.get(groupIndex);

                // 处理产品
                if (productGroup.containsKey('products') && productGroup.get('products') instanceof List<Object>) {
                    List<Object> productsObj = (List<Object>)productGroup.get('products');
                    Integer lineNum = 10;
                    // 处理每个产品，使用当前产品组的Product_Group__c
                    for (Object productObj : productsObj) {
                       
                        if (!(productObj instanceof Map<String, Object>)) {
                            continue;
                        }
                        
                        Map<String, Object> product = (Map<String, Object>)productObj;
                    

                        // 创建新的报价行项目
                        if (!product.containsKey('productId') || product.get('productId') == null) {
                            continue;
                        }
                        
                        String productId = String.valueOf(product.get('productId'));
                        if (String.isBlank(productId)) {
                            continue;
                        }
                        
                        if (!productToPbEntryMap.containsKey(Id.valueOf(productId))) {
                            System.debug('未找到产品的价格表条目: ' + productId);
                            continue;
                        }
                        
                        // 创建新的报价行项目，并设置ISGROUP__c=true标识为产品组内的产品
                        QuoteLineItem quoteLineItem = new QuoteLineItem(
                            QuoteId = quoteId,
                            Product2Id = productId,
                            PricebookEntryId = productToPbEntryMap.get(Id.valueOf(productId)),
                            Quantity = 1,
                            ISGROUP__c = true,  // 设置为产品组内的产品
                            Product_Group__c = pg.Id, // 重要：关联到指定的产品组
                            Tax_Rate__c = 0.00,  // 设置默认税率为0.00
                            Description = ''  // 设置默认备注为空字符串
                        );
                    
                       
                        //设置行编号
                        quoteLineItem.LineNum__c = String.valueOf(lineNum);
                        lineNum += 10;
                        // 设置基准价 - 根据汇率差自动设置，不使用前端传入的值
                        quoteLineItem.UnitPrice = baseUnitPrice;
                        // 设置字段值
                        if (product.containsKey('listPrice')) {
                            String listPriceStr = String.valueOf(product.get('listPrice'));
                            if (String.isNotBlank(listPriceStr)) {
                                quoteLineItem.ListPrice__c = Decimal.valueOf(listPriceStr);
                            }
                        }
                        
                        if (product.containsKey('taxRate')) {
                            String taxRateStr = String.valueOf(product.get('taxRate'));
                            if (String.isNotBlank(taxRateStr)) {
                                quoteLineItem.Tax_Rate__c = Decimal.valueOf(taxRateStr);
                            }
                        }
                        
                        if (product.containsKey('customerAccountId')) {
                            String customerIdStr = String.valueOf(product.get('customerAccountId'));
                            quoteLineItem.Account_ID__c = customerIdStr;
                        }
                        
                        if (product.containsKey('profitDescription')) {
                            String profitDescStr = String.valueOf(product.get('profitDescription'));
                            quoteLineItem.Profit_Statement__c = profitDescStr;
                        }
                        
                        // 处理备注字段
                        if (product.containsKey('Description')) {
                            String descriptionStr = String.valueOf(product.get('Description'));
                            quoteLineItem.Description = descriptionStr;
                        }
                        
                        // 处理服务开始日和服务截止日
                        if (product.containsKey('QuoteLineStartDate')) {
                            String startDateStr = String.valueOf(product.get('QuoteLineStartDate'));
                            if (String.isNotBlank(startDateStr) && startDateStr != 'null') {
                                try {
                                    quoteLineItem.QuoteLineStartDate__c = Date.valueOf(startDateStr);
                                } catch (Exception e) {
                                    System.debug('无效的服务开始日格式: ' + startDateStr);
                                }
                            }
                        }
                        
                        if (product.containsKey('QuoteLineEndDate')) {
                            String endDateStr = String.valueOf(product.get('QuoteLineEndDate'));
                            if (String.isNotBlank(endDateStr) && endDateStr != 'null') {
                                try {
                                    quoteLineItem.QuoteLineEndDate__c = Date.valueOf(endDateStr);
                                } catch (Exception e) {
                                    System.debug('无效的服务截止日格式: ' + endDateStr);
                                }
                            }
                        }
                          // 处理区域字段
                        if (product.containsKey('Region')) {
                            if (regionMap == null) { // 仅初始化一次，避免重复查询
                                // 调用getPicklistValues获取QuoteLineItem对象的Region__c字段的Picklist映射（标签→API值）
                                regionMap = getPicklistValues('QuoteLineItem', 'Region__c');
                                System.debug('动态初始化区域映射regionMap: ' + regionMap);
                            }
                            String regionStr = String.valueOf(product.get('Region'));
                            quoteLineItem.Region__c = regionMap.get(regionStr);
                        }

                        // 处理是否一次性费用字段
                        if (product.containsKey('OneTimeFee')) {
                            String oneTimeFeeStr = String.valueOf(product.get('OneTimeFee'));
                            quoteLineItem.IS_OneTimeFee__c = String.isNotBlank(oneTimeFeeStr) ? oneTimeFeeStr : '否';
                        } else {
                            quoteLineItem.IS_OneTimeFee__c = '否';
                        }

                        // 处理计费类型字段
                        if (product.containsKey('chargeType')) {
                            String chargeTypeStr = String.valueOf(product.get('chargeType'));
                            quoteLineItem.Charge_Type__c = mapChargeTypeLabelToApiName(chargeTypeStr);
                        } else {
                            quoteLineItem.Charge_Type__c = '2'; // 默认为计费
                        }
                        
                        // 处理产品相关字段 - 这些字段通常存储在Product2对象上，不需要直接存储在QuoteLineItem上
                        // 但是，我们可以在前端显示这些信息，所以需要从前端接收并在需要时使用
                        // 如果有自定义字段需要存储这些信息，可以在这里添加代码
                        
                        // 确保设置ISGROUP__c=true，标识为产品组中的产品
                        if (product.containsKey('isGroup')) {
                            Boolean isGroup = Boolean.valueOf(product.get('isGroup'));
                            quoteLineItem.ISGROUP__c = isGroup;
                        } else {
                            // 如果前端没有传递isGroup字段，默认设置为true
                            quoteLineItem.ISGROUP__c = true;
                        }
                        if (product.containsKey('exchangeRateDifferenceValue')) {
                            quoteLineItem.IsExchangeRateDifference__c = String.valueOf(product.get('exchangeRateDifferenceValue'));

                        }
                        insertQLIList.add(quoteLineItem);
                       
                    }
                }
                 Quotation_Method__c qm = new Quotation_Method__c();
                 

                 // 处理产品折扣报价方式
                if (productGroup.containsKey('hasProductDiscountQuote') && 
                    Boolean.valueOf(productGroup.get('hasProductDiscountQuote'))) {
                    
                    String discountCoefficientStr = String.valueOf(productGroup.get('discountCoefficient'));
                    String fixedRebateStr = String.valueOf(productGroup.get('fixedRebate'));
                    String cashReduceStr = String.valueOf(productGroup.get('cashReduce'));
                    String creditStr = String.valueOf(productGroup.get('credit'));
                    Decimal discountCoefficient = String.isNotBlank(discountCoefficientStr) ? Decimal.valueOf(discountCoefficientStr) : 0;
                    Decimal fixedRebate = String.isNotBlank(fixedRebateStr) ? Decimal.valueOf(fixedRebateStr) : 0;
                    Decimal cashReduce = String.isNotBlank(cashReduceStr) ? Decimal.valueOf(cashReduceStr) : 0;
                    Decimal credit = String.isNotBlank(creditStr) ? Decimal.valueOf(creditStr) : 0;
                    
                    System.debug('产品折扣报价方式字段值: discountCoefficient=' + discountCoefficient + 
                               ', fixedRebate=' + fixedRebate + 
                               ', cashReduce=' + cashReduce + 
                               ', credit=' + credit);
                    
        
                        
                    qm= saveQuotationMethod(pg, '2', 
                                         new Map<String, Object>{
                                             'Discount_Factor__c' => discountCoefficient,
                                             'Fixed_Rebate__c' => fixedRebate,
                                             'Cash_Reduce__c' => cashReduce,
                                             'Credit__c' => credit
                                         });
                    }
            

                // 处理1报价方式
                if (productGroup.containsKey('hasMinimumUnitPriceQuote') &&
                    Boolean.valueOf(productGroup.get('hasMinimumUnitPriceQuote'))) {

                    String minimumUnitPriceStr = String.valueOf(productGroup.get('minimumUnitPrice'));
                    String minimumQuantityStr = String.valueOf(productGroup.get('minimumQuantity'));
                    Decimal minimumUnitPrice = String.isNotBlank(minimumUnitPriceStr) ? Decimal.valueOf(minimumUnitPriceStr) : 0;
                    Decimal minimumQuantity = String.isNotBlank(minimumQuantityStr) ? Decimal.valueOf(minimumQuantityStr) : 0;

                    System.debug('产品组1报价方式字段值: minimumUnitPrice=' + minimumUnitPrice +
                               ', minimumQuantity=' + minimumQuantity);

                    qm = saveQuotationMethod(pg, '1',
                                        new Map<String, Object>{
                                            'Minimum_ProdUnitPrice__c' => minimumUnitPrice,
                                            'Minimum_Amout__c' => minimumQuantity
                                     });
                }

                 if (productGroup.containsKey('hasSharedLadderUsagePriceDown') && 
                    Boolean.valueOf(productGroup.get('hasSharedLadderUsagePriceDown'))) {

                    System.debug('处理3报价方式');
                    qm.Method__c = '3';
                    qm.LadderType__c = String.valueOf(productGroup.get('ladderType'));
           
                }

                // 处理保底金额+共享阶梯金额折扣落区组合报价方式
                if (productGroup.containsKey('hasMinAmountSharedLadderAmountDiscountDown') && 
                    Boolean.valueOf(productGroup.get('hasMinAmountSharedLadderAmountDiscountDown'))) {

                    System.debug('处理保底金额+共享阶梯金额折扣落区组合报价方式');
                    String minimumAmountStr = String.valueOf(productGroup.get('minimumAmount'));
                    Decimal minimumAmount = String.isNotBlank(minimumAmountStr) ? Decimal.valueOf(minimumAmountStr) : 0;
                    System.debug('保底金额值: ' + minimumAmount);
                    // 获取保底类型
                    String minimumGuaranteeType = '1'; // 默认为折前
                    if (productGroup.containsKey('minimumGuaranteeType')) {
                        minimumGuaranteeType = String.valueOf(productGroup.get('minimumGuaranteeType'));
                    }

                    // 创建新的保底金额报价方式
                    qm = new Quotation_Method__c(
                        Method__c = '4', // 保底金额+共享阶梯金额折扣
                        LadderType__c = String.valueOf(productGroup.get('ladderType')),
                        GuaranteedMin_Amount__c = minimumAmount,
                        MinimumGuarantee_type__c = minimumGuaranteeType,
                        ISGROUP__c = true // 设置为产品组报价方式
                    );

                }
                
                // 处理1+3组合报价方式
                if (productGroup.containsKey('hasMinUnitPriceSharedLadderUsagePriceDown') && 
                    Boolean.valueOf(productGroup.get('hasMinUnitPriceSharedLadderUsagePriceDown'))) {
                    
                    System.debug('处理1+3组合报价方式');
                    
                    String minimumUnitPriceStr = String.valueOf(productGroup.get('minimumUnitPrice'));
                    String minimumQuantityStr = String.valueOf(productGroup.get('minimumQuantity'));
                    Decimal minimumUnitPrice = String.isNotBlank(minimumUnitPriceStr) ? Decimal.valueOf(minimumUnitPriceStr) : 0;
                    Decimal minimumQuantity = String.isNotBlank(minimumQuantityStr) ? Decimal.valueOf(minimumQuantityStr) : 0;
          
     
                    // 创建新的1+3组合报价方式
                   qm = new Quotation_Method__c(
                        Method__c = '5', // 单价*数量+阶梯用量单价
                        LadderType__c = String.valueOf(productGroup.get('ladderType')),
                        Minimum_ProdUnitPrice__c = minimumUnitPrice,
                        Minimum_Amout__c = minimumQuantity,
                        ISGROUP__c = true// 设置为产品组报价方式

                    );
                        
                   
                }

                // 处理阶梯金额折扣报价方式
                if (productGroup.containsKey('hasSharedLadderAmountDiscountZone') &&
                    Boolean.valueOf(productGroup.get('hasSharedLadderAmountDiscountZone'))) {

                    System.debug('处理阶梯金额折扣报价方式');

                    // 创建新的阶梯金额折扣报价方式
                    qm = new Quotation_Method__c(
                        Method__c = '6', // 阶梯金额折扣
                        LadderType__c = String.valueOf(productGroup.get('ladderType')),
                        ISGROUP__c = true // 设置为产品组报价方式
                    );
                }

                qm.Product_Group__c = pg.Id;
                qm.ISGROUP__c = true;
                insertQMList.add(qm);


                // 处理阶梯报价
                if (productGroup.containsKey('tiers') && productGroup.get('tiers') instanceof List<Object>) {
                    List<Object> tiersObj = (List<Object>)productGroup.get('tiers');
                    
                    // 确定阶梯报价类型
                    String tierUnit = '用量'; // 默认单位
                    String calculationMethod = '分区'; // 默认计算方式
                    
                    // 检查报价方式类型
                    Boolean isSharedLadderAmountDiscountZone = false;
                    Boolean isSharedLadderAmountDiscountDown = false;
                    Boolean isSharedLadderUsagePriceDown = false;
                    Boolean hasMinAmountSharedLadderAmountDiscountDown = false;
                    Boolean hasMinUnitPriceSharedLadderUsagePriceDown = false;
                    
                    // 从前端获取报价方式类型
                    if (productGroup.containsKey('hasSharedLadderAmountDiscountZone')) {
                        isSharedLadderAmountDiscountZone = Boolean.valueOf(productGroup.get('hasSharedLadderAmountDiscountZone'));
                        if (isSharedLadderAmountDiscountZone) {
                            tierUnit = '金额';
                            calculationMethod = '分区';
                        }
                    }
                    
                    if (productGroup.containsKey('hasSharedLadderAmountDiscountDown')) {
                        isSharedLadderAmountDiscountDown = Boolean.valueOf(productGroup.get('hasSharedLadderAmountDiscountDown'));
                        if (isSharedLadderAmountDiscountDown) {
                            tierUnit = '金额';
                            calculationMethod = '落区';
                        }
                    }
                    
                    if (productGroup.containsKey('hasSharedLadderUsagePriceDown')) {
                        isSharedLadderUsagePriceDown = Boolean.valueOf(productGroup.get('hasSharedLadderUsagePriceDown'));
                        if (isSharedLadderUsagePriceDown) {
                            tierUnit = '用量';
                            calculationMethod = '落区';
                        }
                    }
                    
                    // 组合报价方式
                    if (productGroup.containsKey('hasMinAmountSharedLadderAmountDiscountDown')) {
                        hasMinAmountSharedLadderAmountDiscountDown = Boolean.valueOf(productGroup.get('hasMinAmountSharedLadderAmountDiscountDown'));
                        if (hasMinAmountSharedLadderAmountDiscountDown) {
                            tierUnit = '金额';
                            calculationMethod = '落区';
                        }
                    }
                    
                    if (productGroup.containsKey('hasMinUnitPriceSharedLadderUsagePriceDown')) {
                        hasMinUnitPriceSharedLadderUsagePriceDown = Boolean.valueOf(productGroup.get('hasMinUnitPriceSharedLadderUsagePriceDown'));
                        if (hasMinUnitPriceSharedLadderUsagePriceDown) {
                            tierUnit = '用量';
                            calculationMethod = '落区';
                        }
                    }
                    
                    // 记录处理过的阶梯行ID，确保只处理当前产品组的阶梯行
                    Set<Id> currentGroupLadderLineIds = new Set<Id>();
                    
                    // 处理每个阶梯行
                    for (Object tierObj : tiersObj) {
                        if (!(tierObj instanceof Map<String, Object>)) {
                            continue;
                        }
                        
                        Map<String, Object> tier = (Map<String, Object>)tierObj;
                        
                        // 检查是否是现有记录
                        Ladder_Line__c ladderLine = new Ladder_Line__c();
                            
                        // 直接设置必填字段的默认值
                        ladderLine.Down_Limit__c = 0;
                        ladderLine.Up_Limit__c = 9999;
                        ladderLine.Unit__c = tierUnit; // 使用确定的阶梯类型单位
                        ladderLine.Discount__c = 0;
                        ladderLine.Calculation_Method__c = calculationMethod; // 使用确定的阶梯类型计算方式
                        
                        
                        ladderLine.Product_Group__c = pg.Id;
                        ladderLine.ISGROUP__c =true;
                            
                        
                        
                        // 设置字段值
                        if (tier.containsKey('lowerBound')) {
                            String lowerBoundStr = String.valueOf(tier.get('lowerBound'));
                            if (String.isNotBlank(lowerBoundStr)) {
                                ladderLine.Down_Limit__c = Decimal.valueOf(lowerBoundStr);
                            }
                        }
                        
                        if (tier.containsKey('upperBound')) {
                            String upperBoundStr = String.valueOf(tier.get('upperBound'));
                            if (String.isNotBlank(upperBoundStr)) {
                                ladderLine.Up_Limit__c = Decimal.valueOf(upperBoundStr);
                            }
                        }
                        
                        if (tier.containsKey('unit')) {
                            String unitStr = String.valueOf(tier.get('unit'));
                            ladderLine.Unit__c = unitStr;
                            System.debug('设置阶梯行单位: ' + unitStr);
                        } else {
                            // 确保单位字段使用确定的阶梯类型单位
                            ladderLine.Unit__c = tierUnit;
                            System.debug('根据阶梯类型设置单位: ' + tierUnit);
                        }
                        
                        if (tier.containsKey('discount')) {
                            String discountStr = String.valueOf(tier.get('discount'));
                            if (String.isNotBlank(discountStr)) {
                                ladderLine.Discount__c = Decimal.valueOf(discountStr);
                            }
                        }
                        
                        if (tier.containsKey('calculationMethod')) {
                            String calcMethodStr = String.valueOf(tier.get('calculationMethod'));
                            ladderLine.Calculation_Method__c = calcMethodStr;
                        } else {
                            // 确保计算方式字段使用确定的阶梯类型计算方式
                            ladderLine.Calculation_Method__c = calculationMethod;
                            System.debug('根据阶梯类型设置计算方式: ' + calculationMethod);
                        }

                        insertLLList.add(ladderLine);
                    
                    }
                }

                groupIndex++;
            }

            if(insertQMList.size()>0){
                insert insertQMList;
            }
            if(insertLLList.size()>0){
                insert insertLLList;
            }
            if(insertQLIList.size()>0){
                insert insertQLIList;
            }

            // 更新Quote对象的利润率字段
            updateQuoteProfitRate(quoteId);
            returnMsg.put('isSuccess', 'true');
            // return '成功';
        } catch (Exception e) {
            Database.rollback(spoint);
            String errorMsg = '产品组保存失败: ' + e.getMessage() + ' 位置: ' + e.getLineNumber();
            System.debug(LoggingLevel.ERROR, '保存产品组数据时发生异常: ' + errorMsg);
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            // return errorMsg;
            returnMsg.put('msg', errorMsg);
        }
        return returnMsg;
    }
    
    /**
     * 执行DML操作
     * @param processResult 处理结果对象
     */
    private static void executeDMLOperations(ProcessResult processResult) {
        // 插入报价方式
        System.debug('processResult.quotationMethodsToInsert'+ JSON.serialize(processResult.quotationMethodsToInsert));
        if (!processResult.quotationMethodsToInsert.isEmpty()) {
            insert processResult.quotationMethodsToInsert;
            
            // 更新阶梯折扣中的报价方式ID
            for (Product_Group__c ld : processResult.ladderDiscountToQuotationMethod.keySet()) {
                Quotation_Method__c qm = processResult.ladderDiscountToQuotationMethod.get(ld);
                // 添加空值检查，确保qm不为null且有ID
                if (qm != null && qm.Id != null) {
                    ld.QuotationMethod_Ladder__c = qm.Id;
                } else {
                    System.debug('警告: 映射中的Quotation_Method__c为空或没有ID，无法设置Product_Group__c的QuotationMethod_Ladder__c关联');
                }
            }
        }
        
        // 更新报价方式
        if (!processResult.quotationMethodsToUpdate.isEmpty()) {
            // 去除重复ID的记录
            Map<Id, Quotation_Method__c> uniqueQuotationMethods = new Map<Id, Quotation_Method__c>();
            for (Quotation_Method__c qm : processResult.quotationMethodsToUpdate) {
                uniqueQuotationMethods.put(qm.Id, qm);
            }
            System.debug('去重前报价方式更新数量: ' + processResult.quotationMethodsToUpdate.size() + ', 去重后: ' + uniqueQuotationMethods.size());
            update uniqueQuotationMethods.values();
        }
        
        // 插入阶梯折扣
        if (!processResult.ladderDiscountsToInsert.isEmpty()) {
            insert processResult.ladderDiscountsToInsert;
            
            // 更新报价行项目中的阶梯折扣ID
            for (QuoteLineItem qli : processResult.quoteLineItemToLadderDiscount.keySet()) {
                Product_Group__c ld = processResult.quoteLineItemToLadderDiscount.get(qli);
                // 添加空值检查，确保ld不为null且有ID
                if (ld != null && ld.Id != null) {
                    qli.Product_Group__c = ld.Id;
                } else {
                    System.debug('警告: 映射中的Product_Group__c为空或没有ID，无法设置QuoteLineItem的Product_Group__c关联');
                }
            }
            
            // 更新阶梯行中的阶梯折扣ID
            for (Ladder_Line__c ladderLine : processResult.ladderLineToLadderDiscount.keySet()) {
                Product_Group__c ld = processResult.ladderLineToLadderDiscount.get(ladderLine);
                // 添加空值检查，确保ld不为null且有ID
                if (ld != null && ld.Id != null) {
                    ladderLine.Product_Group__c = ld.Id;
                } else {
                    System.debug('警告: 映射中的Product_Group__c为空或没有ID，无法设置LadderLine的Product_Group__c关联');
                }
            }
        }
        
        // 更新阶梯折扣
        if (!processResult.ladderDiscountsToUpdate.isEmpty()) {
            // 去除重复ID的记录
            Map<Id, Product_Group__c> uniqueLadderDiscounts = new Map<Id, Product_Group__c>();
            for (Product_Group__c ld : processResult.ladderDiscountsToUpdate) {
                uniqueLadderDiscounts.put(ld.Id, ld);
            }
            System.debug('去重前阶梯折扣更新数量: ' + processResult.ladderDiscountsToUpdate.size() + ', 去重后: ' + uniqueLadderDiscounts.size());
            update uniqueLadderDiscounts.values();
        }
        
        // 插入报价行项目
        if (!processResult.quoteLineItemsToInsert.isEmpty()) {
            insert processResult.quoteLineItemsToInsert;
            
            // 更新保底金额报价方式中的报价行项目ID
            for (Quotation_Method__c qm : processResult.minimumAmountQuoteToQuoteLineItem.keySet()) {
                QuoteLineItem qli = processResult.minimumAmountQuoteToQuoteLineItem.get(qm);
                // 添加空值检查，确保qli不为null且有ID
                if (qli != null && qli.Id != null) {
                    qm.Quote_Line_Item_ID__c = qli.Id;
                    System.debug('为新插入的报价方式设置QuoteLineItem关联: ' + qli.Id + ', 报价方式类型: ' + qm.Method__c);
                } else {
                    System.debug('警告: 映射中的QuoteLineItem为空或没有ID，无法设置Quotation_Method__c的Quote_Line_Item_ID__c关联');
                }
            }
        }
        
        // 更新报价行项目
        if (!processResult.quoteLineItemsToUpdate.isEmpty()) {
            // 去除重复ID的记录
            Map<Id, QuoteLineItem> uniqueQuoteLineItems = new Map<Id, QuoteLineItem>();
            for (QuoteLineItem qli : processResult.quoteLineItemsToUpdate) {
                uniqueQuoteLineItems.put(qli.Id, qli);
            }
            System.debug('去重前报价行项目更新数量: ' + processResult.quoteLineItemsToUpdate.size() + ', 去重后: ' + uniqueQuoteLineItems.size());
            update uniqueQuoteLineItems.values();
        }
        
        // 插入保底金额报价方式
        if (!processResult.minimumAmountQuotesToInsert.isEmpty()) {
            // 确保所有保底金额报价方式都有正确的属性
            for (Quotation_Method__c qm : processResult.minimumAmountQuotesToInsert) {
                // 根据报价方式类型设置正确的属性
                if (qm.Method__c == '4') {
                // 确保Method__c字段设置正确
                    qm.Method__c = '4';
                // 确保ISGROUP__c字段设置正确
                qm.ISGROUP__c = true;
                // 确保GuaranteedMin_Amount__c不为null
                if (qm.GuaranteedMin_Amount__c == null) {
                    qm.GuaranteedMin_Amount__c = 0;
                }
                    System.debug('准备插入保底金额+共享阶梯金额折扣落区报价方式: Method__c=' + qm.Method__c + 
                           ', GuaranteedMin_Amount__c=' + qm.GuaranteedMin_Amount__c + 
                           ', Quote_Line_Item_ID__c=' + qm.Quote_Line_Item_ID__c +
                           ', ISGROUP__c=' + qm.ISGROUP__c);
                } 
                else if (qm.Method__c == '5') {
                    // 确保Method__c字段设置正确
                    qm.Method__c = '5';
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    // 确保Minimum_ProdUnitPrice__c不为null
                    if (qm.Minimum_ProdUnitPrice__c == null) {
                        qm.Minimum_ProdUnitPrice__c = 0;
                    }
                    // 确保Minimum_Amout__c不为null
                    if (qm.Minimum_Amout__c == null) {
                        qm.Minimum_Amout__c = 0;
                    }
                    System.debug('准备插入1+3报价方式: Method__c=' + qm.Method__c +
                               ', Minimum_ProdUnitPrice__c=' + qm.Minimum_ProdUnitPrice__c + 
                               ', Minimum_Amout__c=' + qm.Minimum_Amout__c + 
                               ', Quote_Line_Item_ID__c=' + qm.Quote_Line_Item_ID__c +
                               ', ISGROUP__c=' + qm.ISGROUP__c);
                }
                else if (qm.Method__c == '6') {
                    // 确保Method__c字段设置正确
                    qm.Method__c = '6';
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    System.debug('准备插入阶梯金额折扣报价方式: Method__c=' + qm.Method__c +
                               ', Quote_Line_Item_ID__c=' + qm.Quote_Line_Item_ID__c +
                               ', ISGROUP__c=' + qm.ISGROUP__c);
                }
                else if (qm.Method__c == '固定金额') {
                    // 确保Method__c字段设置正确
                    if (qm.Method__c == null || qm.Method__c != '固定金额') {
                        qm.Method__c = '固定金额';
                    }
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    // 确保Fixed_Dosage__c不为null
                    if (qm.Fixed_Dosage__c == null) {
                        qm.Fixed_Dosage__c = 0;
                    }
                    // 确保Fixed_UnitPrice__c不为null
                    if (qm.Fixed_UnitPrice__c == null) {
                        qm.Fixed_UnitPrice__c = 0;
                    }
                    System.debug('准备插入固定金额报价方式: Method__c=' + qm.Method__c + 
                               ', Fixed_Dosage__c=' + qm.Fixed_Dosage__c + 
                               ', Fixed_UnitPrice__c=' + qm.Fixed_UnitPrice__c + 
                               ', Quote_Line_Item_ID__c=' + qm.Quote_Line_Item_ID__c +
                               ', ISGROUP__c=' + qm.ISGROUP__c);
                }
                else if (qm.Method__c == '2') {
                    // 确保Method__c字段设置正确
                    if (qm.Method__c == null || qm.Method__c != '2') {
                        qm.Method__c = '2';
                    }
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    System.debug('准备插入产品折扣报价方式: Method__c=' + qm.Method__c + 
                               ', Quote_Line_Item_ID__c=' + qm.Quote_Line_Item_ID__c +
                               ', ISGROUP__c=' + qm.ISGROUP__c);
                }
            }
            
            try {
                insert processResult.minimumAmountQuotesToInsert;
                System.debug('成功插入报价方式，数量: ' + processResult.minimumAmountQuotesToInsert.size());
                
                // 检查是否需要更新刚插入的报价方式的关联关系
                List<Quotation_Method__c> minimumAmountQuotesToUpdateAfterInsert = new List<Quotation_Method__c>();
                
                for (Quotation_Method__c qm : processResult.minimumAmountQuotesToInsert) {
                    boolean needsUpdate = false;
                    
                    // 检查是否需要更新Quote_Line_Item_ID__c
                    if (qm.Quote_Line_Item_ID__c == null && processResult.minimumAmountQuoteToQuoteLineItem.containsKey(qm)) {
                        QuoteLineItem qli = processResult.minimumAmountQuoteToQuoteLineItem.get(qm);
                        if (qli != null && qli.Id != null) {
                            qm.Quote_Line_Item_ID__c = qli.Id;
                            needsUpdate = true;
                            System.debug('更新刚插入的报价方式的Quote_Line_Item_ID__c: ' + qm.Id + ' -> ' + qli.Id + ', 报价方式类型: ' + qm.Method__c);
                        }
                    }
                    
                    if (needsUpdate) {
                        minimumAmountQuotesToUpdateAfterInsert.add(qm);
                    }
                }
                
                // 如果有需要更新的记录，执行更新
                if (!minimumAmountQuotesToUpdateAfterInsert.isEmpty()) {
                    update minimumAmountQuotesToUpdateAfterInsert;
                    System.debug('更新了刚插入的报价方式的关联关系，数量: ' + minimumAmountQuotesToUpdateAfterInsert.size());
                }
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR, '插入报价方式时出错: ' + e.getMessage());
                System.debug(LoggingLevel.ERROR, '错误详情: ' + e.getStackTraceString());
                // 不抛出异常，让代码继续执行
            }
        }
        
        // 更新报价方式
        if (!processResult.minimumAmountQuotesToUpdate.isEmpty()) {
            // 去除重复ID的记录
            Map<Id, Quotation_Method__c> uniqueMinimumAmountQuotes = new Map<Id, Quotation_Method__c>();
            for (Quotation_Method__c qm : processResult.minimumAmountQuotesToUpdate) {
                uniqueMinimumAmountQuotes.put(qm.Id, qm);
                
                // 根据报价方式类型设置正确的属性
                if (qm.Method__c == '4') {
                // 确保Method__c字段设置正确
                    qm.Method__c = '4';
                // 确保ISGROUP__c字段设置正确
                qm.ISGROUP__c = true;
                // 确保GuaranteedMin_Amount__c不为null
                if (qm.GuaranteedMin_Amount__c == null) {
                    qm.GuaranteedMin_Amount__c = 0;
                }
                    System.debug('更新保底金额+共享阶梯金额折扣落区报价方式: ID=' + qm.Id + 
                               ', Method__c=' + qm.Method__c + 
                               ', GuaranteedMin_Amount__c=' + qm.GuaranteedMin_Amount__c);
                }
                else if (qm.Method__c == '5') {
                    // 确保Method__c字段设置正确
                    qm.Method__c = '5';
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    // 确保Minimum_ProdUnitPrice__c不为null
                    if (qm.Minimum_ProdUnitPrice__c == null) {
                        qm.Minimum_ProdUnitPrice__c = 0;
                    }
                    // 确保Minimum_Amout__c不为null
                    if (qm.Minimum_Amout__c == null) {
                        qm.Minimum_Amout__c = 0;
                    }
                    System.debug('更新1+3报价方式: ID=' + qm.Id +
                               ', Method__c=' + qm.Method__c + 
                               ', Minimum_ProdUnitPrice__c=' + qm.Minimum_ProdUnitPrice__c + 
                               ', Minimum_Amout__c=' + qm.Minimum_Amout__c);
                }
                else if (qm.Method__c == '6') {
                    // 确保Method__c字段设置正确
                    qm.Method__c = '6';
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    System.debug('更新阶梯金额折扣报价方式: ID=' + qm.Id +
                               ', Method__c=' + qm.Method__c);
                }
                else if (qm.Method__c == '固定金额') {
                    // 确保Method__c字段设置正确
                    if (qm.Method__c == null || qm.Method__c != '固定金额') {
                        qm.Method__c = '固定金额';
                    }
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                    // 确保Fixed_Dosage__c不为null
                    if (qm.Fixed_Dosage__c == null) {
                        qm.Fixed_Dosage__c = 0;
                    }
                    // 确保Fixed_UnitPrice__c不为null
                    if (qm.Fixed_UnitPrice__c == null) {
                        qm.Fixed_UnitPrice__c = 0;
                    }
                }
                else if (qm.Method__c == '2') {
                    // 确保Method__c字段设置正确
                    if (qm.Method__c == null || qm.Method__c != '2') {
                        qm.Method__c = '2';
                    }
                    // 确保ISGROUP__c字段设置正确
                    qm.ISGROUP__c = true;
                }
            }
            
            System.debug('去重前报价方式更新数量: ' + processResult.minimumAmountQuotesToUpdate.size() + ', 去重后: ' + uniqueMinimumAmountQuotes.size());
            
            try {
                update uniqueMinimumAmountQuotes.values();
                System.debug('成功更新报价方式，数量: ' + uniqueMinimumAmountQuotes.size());
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR, '更新报价方式时出错: ' + e.getMessage());
                System.debug(LoggingLevel.ERROR, '错误详情: ' + e.getStackTraceString());
                System.debug(LoggingLevel.ERROR, '出错的记录: ' + JSON.serialize(uniqueMinimumAmountQuotes.values()));
            }
        }
        
        // 在处理阶梯行之前，建立每个Product_Group__c下的QuoteLineItems，确保产品组隔离
        Map<Id, Set<Id>> ladderDiscountToQuoteLineItemIds = new Map<Id, Set<Id>>();
        
        // 从待插入的QuoteLineItems构建映射
        for (QuoteLineItem qli : processResult.quoteLineItemsToInsert) {
            if (qli.Product_Group__c != null) {
                if (!ladderDiscountToQuoteLineItemIds.containsKey(qli.Product_Group__c)) {
                    ladderDiscountToQuoteLineItemIds.put(qli.Product_Group__c, new Set<Id>());
                }
                // 这里qli.Id可能为null，但在插入后会设置
            }
        }
        
        // 从待更新的QuoteLineItems构建映射
        for (QuoteLineItem qli : processResult.quoteLineItemsToUpdate) {
            if (qli.Product_Group__c != null && qli.Id != null) {
                if (!ladderDiscountToQuoteLineItemIds.containsKey(qli.Product_Group__c)) {
                    ladderDiscountToQuoteLineItemIds.put(qli.Product_Group__c, new Set<Id>());
                }
                ladderDiscountToQuoteLineItemIds.get(qli.Product_Group__c).add(qli.Id);
            }
        }
        
        // 确保每个阶梯行都关联到正确的Product_Group__c
        // 检查待插入的阶梯行
        Map<Id, Product_Group__c> allLadderDiscounts = new Map<Id, Product_Group__c>();
        for (Product_Group__c ld : processResult.ladderDiscountsToInsert) {
            if (ld.Id != null) {
                allLadderDiscounts.put(ld.Id, ld);
            }
        }
        for (Product_Group__c ld : processResult.ladderDiscountsToUpdate) {
            allLadderDiscounts.put(ld.Id, ld);
        }
        
        // 检查待插入的阶梯行
        for (Ladder_Line__c ladderLine : processResult.ladderLinesToInsert) {
            // 确保阶梯行有Product_Group__c关联
            if (ladderLine.Product_Group__c == null) {
                System.debug('警告: 待插入阶梯行缺少Product_Group__c关联');
                // 尝试从映射中找到关联
                if (processResult.ladderLineToLadderDiscount.containsKey(ladderLine)) {
                    Product_Group__c ld = processResult.ladderLineToLadderDiscount.get(ladderLine);
                    if (ld != null && ld.Id != null) {  // 添加空值检查
                        ladderLine.Product_Group__c = ld.Id;
                        System.debug('从映射中设置阶梯行Product_Group__c: ' + ld.Id);
                    } else {
                        System.debug('错误: 映射中的Product_Group__c为空或没有ID');
                    }
                } else {
                    System.debug('错误: 阶梯行不在映射中');
                    
                    // 如果找不到映射，尝试使用第一个可用的Product_Group__c
                    if (!processResult.ladderDiscountsToInsert.isEmpty() && processResult.ladderDiscountsToInsert[0].Id != null) {
                        ladderLine.Product_Group__c = processResult.ladderDiscountsToInsert[0].Id;
                        System.debug('使用第一个可用的Product_Group__c: ' + processResult.ladderDiscountsToInsert[0].Id);
                    } else if (!processResult.ladderDiscountsToUpdate.isEmpty()) {
                        ladderLine.Product_Group__c = processResult.ladderDiscountsToUpdate[0].Id;
                        System.debug('使用第一个更新的Product_Group__c: ' + processResult.ladderDiscountsToUpdate[0].Id);
                    } else {
                        // 创建一个新的Product_Group__c作为最后的手段
                        System.debug('创建新的Product_Group__c作为最后手段');
                        Product_Group__c newLd = new Product_Group__c();
                        insert newLd;
                        ladderLine.Product_Group__c = newLd.Id;
                        System.debug('创建并设置新的Product_Group__c: ' + newLd.Id);
                    }
                }
            }
        }
        
        // 检查待更新的阶梯行
        for (Ladder_Line__c ladderLine : processResult.ladderLinesToUpdate) {
            // 确保更新阶梯行时不会错误地关联到其他产品组的Product_Group__c
            if (ladderLine.Product_Group__c != null && ladderLine.Id != null) {
                System.debug('确认阶梯行' + ladderLine.Id + '使用的Product_Group__c: ' + ladderLine.Product_Group__c);
            }
        }
        
        // 插入阶梯行
        if (!processResult.ladderLinesToInsert.isEmpty()) {
            System.debug('准备插入阶梯行，数量: ' + processResult.ladderLinesToInsert.size());
            
            // 确保所有阶梯行都有正确的Product_Group__c关联 - 这段检查代码在上面已经执行过，不需要重复
            for (Ladder_Line__c ladderLine : processResult.ladderLinesToInsert) {
                // 确保必填字段有值
                if (ladderLine.Unit__c == null) {
                    ladderLine.Unit__c = '用量';
                    System.debug('设置默认单位: 用量');
                }
                if (ladderLine.Calculation_Method__c == null) {
                    ladderLine.Calculation_Method__c = '分区';
                    System.debug('设置默认计算方式: 分区');
                }
                if (ladderLine.Down_Limit__c == null) {
                    ladderLine.Down_Limit__c = 0;
                    System.debug('设置默认下限: 0');
                }
                if (ladderLine.Up_Limit__c == null) {
                    ladderLine.Up_Limit__c = 9999;
                    System.debug('设置默认上限: 9999');
                }
                if (ladderLine.Discount__c == null) {
                    ladderLine.Discount__c = 0;
                    System.debug('设置默认折扣: 0');
                }
                
                // 最终检查
                if (ladderLine.Product_Group__c == null) {
                    System.debug('严重错误: 经过所有尝试后，阶梯行的Product_Group__c仍为空');
                }
            }
            
            try {
               
                
                // 执行插入
                Database.SaveResult[] results = Database.insert(processResult.ladderLinesToInsert, false);
                
                // 分析结果
                Integer successCount = 0;
                for (Integer i = 0; i < results.size(); i++) {
                    Database.SaveResult sr = results[i];
                    if (sr.isSuccess()) {
                        successCount++;
                    } else {
                        for (Database.Error err : sr.getErrors()) {
                            System.debug(LoggingLevel.ERROR, '阶梯行插入错误: ' + err.getStatusCode() + ': ' + err.getMessage() + ', 字段: ' + err.getFields());
                        }
                    }
                }
                
                System.debug('阶梯行插入结果: 成功 ' + successCount + '/' + results.size() + ' 条');
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR, '插入阶梯行时出错: ' + e.getMessage());
                System.debug(LoggingLevel.ERROR, '错误详情: ' + e.getStackTraceString());
               
               
            }
        }
        
        // 更新阶梯行
        if (!processResult.ladderLinesToUpdate.isEmpty()) {
            System.debug('准备更新阶梯行，数量: ' + processResult.ladderLinesToUpdate.size());
            
            // 检查每个阶梯行的字段
            for (Ladder_Line__c ladderLine : processResult.ladderLinesToUpdate) {
                // 确保必填字段有值
                if (ladderLine.Unit__c == null) {
                    ladderLine.Unit__c = '用量';
                    System.debug('更新时设置默认单位: 用量');
                }
                if (ladderLine.Calculation_Method__c == null) {
                    ladderLine.Calculation_Method__c = '分区';
                    System.debug('更新时设置默认计算方式: 分区');
                }
                
                
            }
            
            try {
                // 去除重复ID的记录
                Map<Id, Ladder_Line__c> uniqueLadderLines = new Map<Id, Ladder_Line__c>();
                for (Ladder_Line__c ladderLine : processResult.ladderLinesToUpdate) {
                    uniqueLadderLines.put(ladderLine.Id, ladderLine);
                }
                System.debug('去重前阶梯行更新数量: ' + processResult.ladderLinesToUpdate.size() + ', 去重后: ' + uniqueLadderLines.size());
                
                // 使用Database.update以获取详细结果
                Database.SaveResult[] results = Database.update(uniqueLadderLines.values(), false);
                
                // 分析结果
                Integer successCount = 0;
                for (Integer i = 0; i < results.size(); i++) {
                    Database.SaveResult sr = results[i];
                    if (sr.isSuccess()) {
                        successCount++;
                    } else {
                        for (Database.Error err : sr.getErrors()) {
                            System.debug(LoggingLevel.ERROR, '阶梯行更新错误: ' + err.getStatusCode() + ': ' + err.getMessage() + ', 字段: ' + err.getFields());
                        }
                    }
                }
                
                System.debug('阶梯行更新结果: 成功 ' + successCount + '/' + results.size() + ' 条');
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR, '更新阶梯行时出错: ' + e.getMessage());
                System.debug(LoggingLevel.ERROR, '错误详情: ' + e.getStackTraceString());
                // 不抛出异常，让代码继续执行
                System.debug('继续执行其他DML操作');
            }
        }
        
        // 删除阶梯行
        if (!processResult.ladderLinesToDelete.isEmpty()) {
            // 去除重复ID的记录
            Set<Id> uniqueLadderLineIds = new Set<Id>();
            List<Ladder_Line__c> uniqueLadderLines = new List<Ladder_Line__c>();
            for (Ladder_Line__c ladderLine : processResult.ladderLinesToDelete) {
                if (!uniqueLadderLineIds.contains(ladderLine.Id)) {
                    uniqueLadderLineIds.add(ladderLine.Id);
                    uniqueLadderLines.add(ladderLine);
                }
            }
            System.debug('去重前阶梯行删除数量: ' + processResult.ladderLinesToDelete.size() + ', 去重后: ' + uniqueLadderLines.size());
            delete uniqueLadderLines;
        }
        
        // 删除保底金额报价方式
        if (!processResult.minimumAmountQuotesToDelete.isEmpty()) {
            // 去除重复ID的记录
            Set<Id> uniqueMinimumAmountQuoteIds = new Set<Id>();
            List<Quotation_Method__c> uniqueMinimumAmountQuotes = new List<Quotation_Method__c>();
            for (Quotation_Method__c qm : processResult.minimumAmountQuotesToDelete) {
                if (!uniqueMinimumAmountQuoteIds.contains(qm.Id)) {
                    uniqueMinimumAmountQuoteIds.add(qm.Id);
                    uniqueMinimumAmountQuotes.add(qm);
                }
            }
            System.debug('去重前保底金额报价方式删除数量: ' + processResult.minimumAmountQuotesToDelete.size() + ', 去重后: ' + uniqueMinimumAmountQuotes.size());
            delete uniqueMinimumAmountQuotes;
        }
        
        // 删除报价行项目
        if (!processResult.quoteLineItemsToDelete.isEmpty()) {
            // 去除重复ID的记录
            Set<Id> uniqueQuoteLineItemIds = new Set<Id>();
            List<QuoteLineItem> uniqueQuoteLineItems = new List<QuoteLineItem>();
            for (QuoteLineItem qli : processResult.quoteLineItemsToDelete) {
                if (!uniqueQuoteLineItemIds.contains(qli.Id)) {
                    uniqueQuoteLineItemIds.add(qli.Id);
                    uniqueQuoteLineItems.add(qli);
                }
            }
            System.debug('去重前报价行项目删除数量: ' + processResult.quoteLineItemsToDelete.size() + ', 去重后: ' + uniqueQuoteLineItems.size());
            delete uniqueQuoteLineItems;
        }
        
        // 删除阶梯折扣
        if (!processResult.ladderDiscountsToDelete.isEmpty()) {
            // 去除重复ID的记录
            Set<Id> uniqueLadderDiscountIds = new Set<Id>();
            List<Product_Group__c> uniqueLadderDiscounts = new List<Product_Group__c>();
            for (Product_Group__c ld : processResult.ladderDiscountsToDelete) {
                if (!uniqueLadderDiscountIds.contains(ld.Id)) {
                    uniqueLadderDiscountIds.add(ld.Id);
                    uniqueLadderDiscounts.add(ld);
                }
            }
            System.debug('去重前阶梯折扣删除数量: ' + processResult.ladderDiscountsToDelete.size() + ', 去重后: ' + uniqueLadderDiscounts.size());
            delete uniqueLadderDiscounts;
        }
        
        // 删除报价方式
        if (!processResult.quotationMethodsToDelete.isEmpty()) {
            // 去除重复ID的记录
            Set<Id> uniqueQuotationMethodIds = new Set<Id>();
            List<Quotation_Method__c> uniqueQuotationMethods = new List<Quotation_Method__c>();
            for (Quotation_Method__c qm : processResult.quotationMethodsToDelete) {
                if (!uniqueQuotationMethodIds.contains(qm.Id)) {
                    uniqueQuotationMethodIds.add(qm.Id);
                    uniqueQuotationMethods.add(qm);
                }
            }
            System.debug('去重前报价方式删除数量: ' + processResult.quotationMethodsToDelete.size() + ', 去重后: ' + uniqueQuotationMethods.size());
            delete uniqueQuotationMethods;
        }
    }
    
    /**
     * 处理结果类
     */
    private class ProcessResult {
        // 报价方式
        public List<Quotation_Method__c> quotationMethodsToInsert = new List<Quotation_Method__c>();
        public List<Quotation_Method__c> quotationMethodsToUpdate = new List<Quotation_Method__c>();
        public List<Quotation_Method__c> quotationMethodsToDelete = new List<Quotation_Method__c>();
        
        // 阶梯折扣
        public List<Product_Group__c> ladderDiscountsToInsert = new List<Product_Group__c>();
        public List<Product_Group__c> ladderDiscountsToUpdate = new List<Product_Group__c>();
        public List<Product_Group__c> ladderDiscountsToDelete = new List<Product_Group__c>();
        
        // 报价行项目
        public List<QuoteLineItem> quoteLineItemsToInsert = new List<QuoteLineItem>();
        public List<QuoteLineItem> quoteLineItemsToUpdate = new List<QuoteLineItem>();
        public List<QuoteLineItem> quoteLineItemsToDelete = new List<QuoteLineItem>();
        
        // 阶梯行
        public List<Ladder_Line__c> ladderLinesToInsert = new List<Ladder_Line__c>();
        public List<Ladder_Line__c> ladderLinesToUpdate = new List<Ladder_Line__c>();
        public List<Ladder_Line__c> ladderLinesToDelete = new List<Ladder_Line__c>();
        
        // 所有类型的报价方式（包括保底金额、保底单价*保底数量、固定金额、产品折扣等）
        public List<Quotation_Method__c> minimumAmountQuotesToInsert = new List<Quotation_Method__c>();
        public List<Quotation_Method__c> minimumAmountQuotesToUpdate = new List<Quotation_Method__c>();
        public List<Quotation_Method__c> minimumAmountQuotesToDelete = new List<Quotation_Method__c>();
        
        // 引用关系映射
        public Map<Product_Group__c, Quotation_Method__c> ladderDiscountToQuotationMethod = new Map<Product_Group__c, Quotation_Method__c>();
        public Map<QuoteLineItem, Product_Group__c> quoteLineItemToLadderDiscount = new Map<QuoteLineItem, Product_Group__c>();
        public Map<Ladder_Line__c, Product_Group__c> ladderLineToLadderDiscount = new Map<Ladder_Line__c, Product_Group__c>();
        public Map<Quotation_Method__c, QuoteLineItem> minimumAmountQuoteToQuoteLineItem = new Map<Quotation_Method__c, QuoteLineItem>();
        public Map<Ladder_Line__c, QuoteLineItem> ladderLineToQuoteLineItem = new Map<Ladder_Line__c, QuoteLineItem>();
        // 产品组映射 - 用于跟踪每个产品组共享的Product_Group__c
        public Map<String, Product_Group__c> productGroupToLadderDiscount = new Map<String, Product_Group__c>();
        
        // 添加调试方法，打印当前处理结果状态
        public void printDebugInfo() {
            System.debug('===== 处理结果状态 =====');
            System.debug('阶梯报价方式 - 插入: ' + quotationMethodsToInsert.size() + ', 更新: ' + quotationMethodsToUpdate.size() + ', 删除: ' + quotationMethodsToDelete.size());
            System.debug('阶梯折扣 - 插入: ' + ladderDiscountsToInsert.size() + ', 更新: ' + ladderDiscountsToUpdate.size() + ', 删除: ' + ladderDiscountsToDelete.size());
            System.debug('报价行项目 - 插入: ' + quoteLineItemsToInsert.size() + ', 更新: ' + quoteLineItemsToUpdate.size() + ', 删除: ' + quoteLineItemsToDelete.size());
            System.debug('阶梯行 - 插入: ' + ladderLinesToInsert.size() + ', 更新: ' + ladderLinesToUpdate.size() + ', 删除: ' + ladderLinesToDelete.size());
            System.debug('所有报价方式 - 插入: ' + minimumAmountQuotesToInsert.size() + ', 更新: ' + minimumAmountQuotesToUpdate.size() + ', 删除: ' + minimumAmountQuotesToDelete.size());
            
            // 打印报价方式的详细信息
            System.debug('报价方式详情:');
            for (Quotation_Method__c qm : minimumAmountQuotesToInsert) {
                System.debug('待插入 - ID: ' + qm.Id + ', 方法: ' + qm.Method__c + ', 关联产品: ' + qm.Quote_Line_Item_ID__c);
                
                // 打印保底金额报价方式的保底金额
                if (qm.Method__c == '保底金额') {
                    System.debug('保底金额: ' + qm.GuaranteedMin_Amount__c);
                }
                
                // 打印保底单价*保底数量报价方式的保底单价和保底数量
                if (qm.Method__c == '保底单价*保底数量') {
                    System.debug('保底单价: ' + qm.Minimum_ProdUnitPrice__c + ', 保底数量: ' + qm.Minimum_Amout__c);
                }
            }
            
            for (Quotation_Method__c qm : minimumAmountQuotesToUpdate) {
                System.debug('待更新 - ID: ' + qm.Id + ', 方法: ' + qm.Method__c + ', 关联产品: ' + qm.Quote_Line_Item_ID__c);
                
                // 打印保底金额报价方式的保底金额
                if (qm.Method__c == '保底金额') {
                    System.debug('保底金额: ' + qm.GuaranteedMin_Amount__c);
            }
                
                // 这里不再需要检查"保底单价*保底数量"报价方式，因为它不是有效的报价方式
            }
            
            for (Quotation_Method__c qm : minimumAmountQuotesToDelete) {
                System.debug('待删除 - ID: ' + qm.Id + ', 方法: ' + qm.Method__c + ', 关联产品: ' + qm.Quote_Line_Item_ID__c);
            }
        }
    }
    
    /**
     * 获取当前产品组的产品列表
     */
    private static List<QuoteLineItem> getCurrentGroupProducts(ProcessResult processResult, Product_Group__c groupLadderDiscount) {
        List<QuoteLineItem> currentGroupProducts = new List<QuoteLineItem>();
        
        // 收集当前产品组的产品 - 优先使用已有ID的产品
        for (QuoteLineItem qli : processResult.quoteLineItemsToUpdate) {
            // 使用Product_Group__c来判断是否属于当前产品组
            if (groupLadderDiscount != null && qli.Product_Group__c == groupLadderDiscount.Id) {
                currentGroupProducts.add(qli);
            }
        }
        
        // 如果没有找到已更新的产品，尝试从待插入的产品中查找
        if (currentGroupProducts.isEmpty()) {
            for (QuoteLineItem qli : processResult.quoteLineItemsToInsert) {
                if (processResult.quoteLineItemToLadderDiscount.containsKey(qli)) {
                    Product_Group__c mappedLadderDiscount = processResult.quoteLineItemToLadderDiscount.get(qli);
                    if (mappedLadderDiscount == groupLadderDiscount) {
                        currentGroupProducts.add(qli);
                    }
                }
            }
        }
        
        return currentGroupProducts;
    }
    
    /**
     * 保存报价方式
     */
    private static Quotation_Method__c saveQuotationMethod(Product_Group__c pg, String methodType, Map<String, Object> fieldValues) {
   
        Quotation_Method__c qm = new Quotation_Method__c(
            Method__c = methodType,  // 确保设置正确的方法类型
            ISGROUP__c = true,
            Product_Group__c=pg.Id
        );
        
        System.debug('保存报价方式 - 创建新报价方式，方法类型: ' + methodType);
        
        // 设置字段值
        for (String fieldName : fieldValues.keySet()) {
            if (fieldName == 'Minimum_ProdUnitPrice__c') {
                qm.Minimum_ProdUnitPrice__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Minimum_ProdUnitPrice__c = ' + qm.Minimum_ProdUnitPrice__c);
            } else if (fieldName == 'Minimum_Amout__c') {
                qm.Minimum_Amout__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Minimum_Amout__c = ' + qm.Minimum_Amout__c);
            } else if (fieldName == 'GuaranteedMin_Amount__c') {
                qm.GuaranteedMin_Amount__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 GuaranteedMin_Amount__c = ' + qm.GuaranteedMin_Amount__c);
            } else if (fieldName == 'Fixed_Dosage__c') {
                qm.Fixed_Dosage__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Fixed_Dosage__c = ' + qm.Fixed_Dosage__c);
            } else if (fieldName == 'Fixed_UnitPrice__c') {
                qm.Fixed_UnitPrice__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Fixed_UnitPrice__c = ' + qm.Fixed_UnitPrice__c);
            } else if (fieldName == 'Discount_Factor__c') {
                qm.Discount_Factor__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Discount_Factor__c = ' + qm.Discount_Factor__c);
            } else if (fieldName == 'Fixed_Rebate__c') {
                qm.Fixed_Rebate__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Fixed_Rebate__c = ' + qm.Fixed_Rebate__c);
            } else if (fieldName == 'Cash_Reduce__c') {
                qm.Cash_Reduce__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Cash_Reduce__c = ' + qm.Cash_Reduce__c);
            } else if (fieldName == 'Credit__c') {
                qm.Credit__c = (Decimal)fieldValues.get(fieldName);
                System.debug('设置 Credit__c = ' + qm.Credit__c);
            }
        }

        return qm;

    }
    
    /**
     * 删除报价方式
     */
    private static void deleteQuotationMethods(Product_Group__c groupLadderDiscount, 
                                             ProcessResult processResult, 
                                             Map<Id, Quotation_Method__c> existingQuotations) {
        List<QuoteLineItem> groupProductsToCheck = new List<QuoteLineItem>();
        
        // 找出当前产品组的所有产品
        for (QuoteLineItem qli : processResult.quoteLineItemsToUpdate) {
            if (groupLadderDiscount != null && qli.Product_Group__c == groupLadderDiscount.Id) {
                groupProductsToCheck.add(qli);
            }
        }
        
        // 如果没有找到任何更新的产品，也检查待插入的产品
        if (groupProductsToCheck.isEmpty()) {
            for (QuoteLineItem qli : processResult.quoteLineItemsToInsert) {
                if (processResult.quoteLineItemToLadderDiscount.containsKey(qli) && 
                    processResult.quoteLineItemToLadderDiscount.get(qli) == groupLadderDiscount) {
                    groupProductsToCheck.add(qli);
                }
            }
        }
        
        // 如果还是没有找到产品，尝试通过查询获取
        if (groupProductsToCheck.isEmpty() && groupLadderDiscount != null && groupLadderDiscount.Id != null) {
            try {
                List<QuoteLineItem> existingProducts = [
                    SELECT Id, Product_Group__c
                    FROM QuoteLineItem
                    WHERE Product_Group__c = :groupLadderDiscount.Id
                    AND ISGROUP__c = true
                    LIMIT 10
                ];
                groupProductsToCheck.addAll(existingProducts);
                System.debug('通过查询找到产品组中的产品: ' + existingProducts.size() + '条');
            } catch (Exception e) {
                System.debug('查询产品时发生错误: ' + e.getMessage());
            }
        }
        
        // 检查每个产品是否有报价方式需要删除
        for (QuoteLineItem qli : groupProductsToCheck) {
            if (qli.Id != null && existingQuotations.containsKey(qli.Id)) {
                Quotation_Method__c qmToDelete = existingQuotations.get(qli.Id);
                // 记录报价方式类型，用于调试
                System.debug('找到报价方式需要删除: ID=' + qmToDelete.Id + 
                           ', Method__c=' + qmToDelete.Method__c + 
                           ', 关联产品: ' + qli.Id);
                
                processResult.minimumAmountQuotesToDelete.add(qmToDelete);
                System.debug('删除报价方式，ID: ' + qmToDelete.Id + 
                           ', 类型: ' + qmToDelete.Method__c + 
                           ', 关联到产品: ' + qli.Id);
            } else if (qli.Id != null) {
                // 对于组合报价方式和基本阶梯报价方式，需要额外查询，因为存储方式可能不同
                try {
                    // 查询所有可能的组合报价方式和基本阶梯报价方式
                    List<Quotation_Method__c> allQuotes = [
                        SELECT Id, Quote_Line_Item_ID__c,LadderType__c, Method__c, ISGROUP__c
                        FROM Quotation_Method__c
                        WHERE Quote_Line_Item_ID__c = :qli.Id
                        AND ISGROUP__c = true
                    ];
                    
                    if (!allQuotes.isEmpty()) {
                        for (Quotation_Method__c quote : allQuotes) {
                            System.debug('找到报价方式需要删除: ID=' + quote.Id + 
                                       ', Method__c=' + quote.Method__c + 
                                       ', 关联产品: ' + qli.Id);
                            
                            processResult.minimumAmountQuotesToDelete.add(quote);
                            System.debug('删除报价方式，ID: ' + quote.Id + 
                                       ', 类型: ' + quote.Method__c + 
                                       ', 关联到产品: ' + qli.Id);
                        }
                    } else {
                        System.debug('未找到产品 ' + qli.Id + ' 关联的报价方式');
                    }
                } catch (Exception e) {
                    System.debug('查询报价方式时发生错误: ' + e.getMessage());
                }
            }
        }
        
        // 如果没有找到任何产品，但依然需要清理报价方式，则按照Product_Group__c查询所有相关的报价方式
        if (groupProductsToCheck.isEmpty() && groupLadderDiscount != null && groupLadderDiscount.Id != null) {
            try {
                // 首先获取产品组下的产品ID
                List<Id> productIds = new List<Id>();
                for (QuoteLineItem qli : [
                    SELECT Id
                    FROM QuoteLineItem
                    WHERE Product_Group__c = :groupLadderDiscount.Id
                    AND ISGROUP__c = true
                ]) {
                    productIds.add(qli.Id);
                }
                
                if (!productIds.isEmpty()) {
                    // 查询所有相关的报价方式
                    List<Quotation_Method__c> allQuotes = [
                        SELECT Id, Quote_Line_Item_ID__c,LadderType__c, Method__c, ISGROUP__c
                        FROM Quotation_Method__c
                        WHERE Quote_Line_Item_ID__c IN :productIds
                        AND ISGROUP__c = true
                    ];
                    
                    for (Quotation_Method__c qm : allQuotes) {
                        System.debug('根据产品组关系找到报价方式需要删除: ID=' + qm.Id + 
                                   ', Method__c=' + qm.Method__c + 
                                   ', 关联产品: ' + qm.Quote_Line_Item_ID__c);
                        
                        processResult.minimumAmountQuotesToDelete.add(qm);
                        System.debug('删除报价方式，ID: ' + qm.Id + 
                                   ', 类型: ' + qm.Method__c + 
                                   ', 关联到产品: ' + qm.Quote_Line_Item_ID__c);
                    }
                }
                
                // 同时需要检查QuotationMethod_Ladder__c指向的报价方式
                if (groupLadderDiscount.QuotationMethod_Ladder__c != null) {
                    Quotation_Method__c quotationMethod = new Quotation_Method__c(
                        Id = groupLadderDiscount.QuotationMethod_Ladder__c
                    );
                    
                    // 判断是否已经添加过
                    boolean alreadyAdded = false;
                    for (Quotation_Method__c existingQm : processResult.quotationMethodsToDelete) {
                        if (existingQm.Id == quotationMethod.Id) {
                            alreadyAdded = true;
                            break;
                        }
                    }
                    
                    if (!alreadyAdded) {
                        processResult.quotationMethodsToDelete.add(quotationMethod);
                        System.debug('删除产品组关联的报价方式: ' + quotationMethod.Id);
                    }
                }
            } catch (Exception e) {
                System.debug('查询产品组下所有报价方式时发生错误: ' + e.getMessage());
            }
        }
    }
    


    
    @AuraEnabled
    public static Map<String, Object> getQuoteInfo(String quoteId){
        try {
            // 查询报价单信息
            Quote quote = [
                SELECT Id, Name,toLabel(Product_Cate__c) type,Opportunity.Account_ID__c, QuoteNumber, Profit_Rate__c, StartDate__c, EndDate__c, OpportunityId, AccountId, Account.Name, TotalPrice,Contract_Cur__c,Settlement_Cur__c
                FROM Quote
                WHERE Id = :quoteId
                LIMIT 1
            ];
            
            // 创建返回的Map对象
            Map<String, Object> quoteInfo = new Map<String, Object>();
            quoteInfo.put('id', quote.Id);
            quoteInfo.put('name', quote.Name);
            quoteInfo.put('quoteNumber', quote.QuoteNumber);
            quoteInfo.put('opportunity', quote.OpportunityId);
            quoteInfo.put('startDate', quote.StartDate__c);
            quoteInfo.put('endDate', quote.EndDate__c);
            quoteInfo.put('accountId', quote.AccountId);
            quoteInfo.put('accountName', quote.Account.Name);
            quoteInfo.put('totalPrice', quote.TotalPrice);
            quoteInfo.put('level', quote.get('type'));
            if (quote.Contract_Cur__c == quote.Settlement_Cur__c) {
                quoteInfo.put('exchangeRateDifferenceValue', '2');
                quoteInfo.put('exchangeRateDifferenceLabel', '否');

            }else {
                quoteInfo.put('exchangeRateDifferenceValue', '1');
                quoteInfo.put('exchangeRateDifferenceLabel', '是');

            }
            
            return quoteInfo;
        } catch(Exception e) {
            System.debug('获取报价单信息时发生错误: ' + e.getMessage());
            throw new AuraHandledException('获取报价单信息时发生错误: ' + e.getMessage());
        }
    }

    // /**
    //  * 更新报价单的服务开始日和服务截止日
    //  * @param quoteId 报价单ID
    //  * @param startDate 服务开始日
    //  * @param endDate 服务截止日
    //  * @return 操作结果
    //  */
    // @AuraEnabled
    // public static String updateQuoteDates(String quoteId, String startDate, String endDate) {
    //     System.debug('更新报价单日期，报价ID: ' + quoteId);
    //     System.debug('服务开始日: ' + startDate + ', 服务截止日: ' + endDate);
        
    //     try {
    //         // 查询报价单
    //         Quote quote = [SELECT Id, StartDate__c, EndDate__c FROM Quote WHERE Id = :quoteId LIMIT 1];
            
    //         // 更新日期字段
    //         if (startDate != null) {
    //             quote.StartDate__c = Date.valueOf(startDate);
    //         }
            
    //         if (endDate != null) {
    //             quote.EndDate__c = Date.valueOf(endDate);
    //         }
            
    //         // 保存更新
    //         update quote;
            
    //         return '报价单日期更新成功';
    //     } catch (Exception e) {
    //         System.debug('更新报价单日期失败: ' + e.getMessage());
    //         throw new AuraHandledException('更新报价单日期失败: ' + e.getMessage());
    //     }
    // }

    @AuraEnabled
    public static void deleteProductsByLevelThreeName(String productGroupId, String levelThreeName) {
        if (String.isBlank(productGroupId) || String.isBlank(levelThreeName)) {
            throw new AuraHandledException('Product Group ID and Level 3 Name cannot be null.');
        }

        List<QuoteLineItem> itemsToDelete = [
            SELECT Id, QuoteId
            FROM QuoteLineItem
            WHERE Product_Group__c = :productGroupId
            AND (
                (Product2.Level__c = '3' AND Product2.ParentProduct__r.ParentProduct__r.Name = 'MAAS' AND Product2.Name = :levelThreeName) OR
                (Product2.Level__c = '4' AND Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name = 'MAAS' AND Product2.ParentProduct__r.Name = :levelThreeName)
            )
        ];

        if (!itemsToDelete.isEmpty()) {
            // 获取 quoteId 用于后续更新利润率
            String quoteId = itemsToDelete[0].QuoteId;

            delete itemsToDelete;

            // 删除后更新Quote对象的利润率字段
            // if (String.isNotBlank(quoteId)) {
            //     updateQuoteProfitRate(quoteId);
            // }
        }
    }


    /**
     * 保存单产品报价数据
     * @param singleProductsJSON 单产品报价数据JSON
     * @param quoteId 报价单ID
     * @return 操作结果消息
     */
    @AuraEnabled(cacheable=false)
    public static Map<String,String> saveSingleProductQuote(String singleProductsJSON, String quoteId) {
        Map<String,String> returnMsg = new Map<String,String>();
        returnMsg.put('isSuccess', 'false');
        returnMsg.put('msg', '');
        System.debug('保存单个产品数据，报价ID: ' + quoteId);
        System.debug('接收到的JSON数据: ' + singleProductsJSON);
        Savepoint spoint = Database.setSavepoint();
        
        try {
            // 解析JSON数据
            List<Object> singleProductsList = (List<Object>) JSON.deserializeUntyped(singleProductsJSON);
            System.debug('解析后的单个产品数量: ' + singleProductsList.size());
            
            // 获取报价单的价格手册 Id 和汇率差信息
            Id quotePricebookId;
            String currencyCode;
            Boolean hasExchangeRateDifference = false;
            Decimal baseUnitPrice = 1.0; // 默认基准价

            try {
                Quote quote = [SELECT Pricebook2Id, CurrencyIsoCode, Contract_Cur__c, Settlement_Cur__c FROM Quote WHERE Id = :quoteId LIMIT 1];
                quotePricebookId = quote.Pricebook2Id;
                currencyCode = quote.CurrencyIsoCode;

                // 判断是否存在汇率差
                hasExchangeRateDifference = (quote.Contract_Cur__c != quote.Settlement_Cur__c);

                // 根据汇率差设置基准价
                if (hasExchangeRateDifference) {
                    baseUnitPrice = 1.02; // 有汇率差时设置为1.02
                } else {
                    baseUnitPrice = 1.0;   // 没有汇率差时设置为1
                }

                System.debug('是否存在汇率差: ' + hasExchangeRateDifference);
                System.debug('基准价设置为: ' + baseUnitPrice);

            } catch(Exception ex){
                throw new AuraHandledException('无法获取报价单的价格手册信息: '+ex.getMessage());
            }

            // 收集所有产品 Id，用于查询 PricebookEntry
            Set<Id> allProductIds = new Set<Id>();
            for(Object spObj : singleProductsList){
                Map<String,Object> sp = (Map<String,Object>)spObj;
                if(sp.containsKey('productId') && sp.get('productId')!=null){
                    String pidStr = String.valueOf(sp.get('productId'));
                    try{ allProductIds.add(Id.valueOf(pidStr)); }catch(Exception e){}
                }
            }

            // 查询价格表条目
            Map<Id,Id> productToPbEntry = new Map<Id,Id>();
            if(!allProductIds.isEmpty()){
                for(PricebookEntry pbe : [SELECT Id, Product2Id FROM PricebookEntry WHERE Pricebook2Id = :quotePricebookId AND Product2Id IN :allProductIds AND IsActive = true and CurrencyIsoCode =:currencyCode]){
                    productToPbEntry.put(pbe.Product2Id, pbe.Id);
                }
            }


            //先删除存在该quote下的Quotation_Method__c，Ladder_Line__c，QuoteLineItem
            List<Quotation_Method__c> deleteQMList = [
                SELECT Id, Quote_Line_Item_ID__c,LadderType__c, Method__c, ISGROUP__c,
                       Fixed_Dosage__c, Fixed_UnitPrice__c, Discount_Factor__c, Fixed_Rebate__c,
                       Cash_Reduce__c, Credit__c, GuaranteedMin_Amount__c, Minimum_ProdUnitPrice__c, Minimum_Amout__c
                FROM Quotation_Method__c
                WHERE Quote_Line_Item_ID__c IN (SELECT Id FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null)
                AND ISGROUP__c = false
            ];
            List<Ladder_Line__c> deleteLLList= [SELECT Id, QuoteLineItem__c, Down_Limit__c, Up_Limit__c, Unit__c, Discount__c, Calculation_Method__c
                                                    FROM Ladder_Line__c
                                                    WHERE QuoteLineItem__c IN (SELECT Id FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null)];
            List<QuoteLineItem> deleteQLIList = [SELECT Id FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null];


            if(deleteQMList.size()>0){
                delete deleteQMList;
            }
            if(deleteLLList.size()>0){
                delete deleteLLList;
            }
            if(deleteQLIList.size()>0){
                delete deleteQLIList;
            }

            //再新建前端界面上的Quotation_Method__c，Ladder_Line__c，QuoteLineItem数据
            Map<Integer,QuoteLineItem> insertQLIMap = new Map<Integer,QuoteLineItem>();
            List<Quotation_Method__c> insertQMList = new List<Quotation_Method__c>();
            List<Ladder_Line__c> insertLLList = new List<Ladder_Line__c>();
            List<String> missingPbProducts = new List<String>();
            // 处理每个单个产品
            Integer objNo = 1;
            for (Object singleProductObj : singleProductsList) {
                Map<String, Object> singleProduct = (Map<String, Object>)singleProductObj;
                
                // 获取产品ID
                String productId = String.valueOf(singleProduct.get('id'));
                Boolean isExistingProduct = false;
                
                QuoteLineItem quoteLineItem = new QuoteLineItem(
                        QuoteId = quoteId,
                        ISGROUP__c = false,
                        Quantity = 1
                    );
                if (singleProduct.containsKey('exchangeRateDifferenceValue')) {
                    quoteLineItem.IsExchangeRateDifference__c =String.valueOf(singleProduct.get('exchangeRateDifferenceValue'));
                }
                
                // 设置产品字段
                if (singleProduct.containsKey('productId')) {
                    String product2Id = String.valueOf(singleProduct.get('productId'));
                    quoteLineItem.Product2Id = product2Id;
                }
                
                // 设置基准价 - 根据汇率差自动设置，不使用前端传入的值
                quoteLineItem.UnitPrice = baseUnitPrice;
                // 设置其他字段
                if (singleProduct.containsKey('listPrice')) {
                    String listPriceStr = String.valueOf(singleProduct.get('listPrice'));
                    if (String.isNotBlank(listPriceStr)) {
                        quoteLineItem.ListPrice__c = Decimal.valueOf(listPriceStr);
                    }
                }
                
                if (singleProduct.containsKey('taxRate')) {
                    String taxRateStr = String.valueOf(singleProduct.get('taxRate'));
                    if (String.isNotBlank(taxRateStr)) {
                        quoteLineItem.Tax_Rate__c = Decimal.valueOf(taxRateStr);
                    }
                }
                
                if (singleProduct.containsKey('customerAccountId')) {
                    String customerIdStr = String.valueOf(singleProduct.get('customerAccountId'));
                    quoteLineItem.Account_ID__c = customerIdStr;
                }
                
                if (singleProduct.containsKey('profitDescription')) {
                    String profitDescStr = String.valueOf(singleProduct.get('profitDescription'));
                    quoteLineItem.Profit_Statement__c = profitDescStr;
                }
                
                if (singleProduct.containsKey('Description')) {
                    String descriptionStr = String.valueOf(singleProduct.get('Description'));
                    quoteLineItem.Description = descriptionStr;
                }
                
                if (singleProduct.containsKey('QuoteLineStartDate')) {
                    String startDateStr = String.valueOf(singleProduct.get('QuoteLineStartDate'));
                    if (String.isNotBlank(startDateStr) && startDateStr != 'null') {
                        try {
                            quoteLineItem.QuoteLineStartDate__c = Date.valueOf(startDateStr);
                        } catch (Exception e) {
                            System.debug('无效的服务开始日格式: ' + startDateStr);
                        }
                    }
                }
                
                if (singleProduct.containsKey('QuoteLineEndDate')) {
                    String endDateStr = String.valueOf(singleProduct.get('QuoteLineEndDate'));
                    if (String.isNotBlank(endDateStr) && endDateStr != 'null') {
                        try {
                            quoteLineItem.QuoteLineEndDate__c = Date.valueOf(endDateStr);
                        } catch (Exception e) {
                            System.debug('无效的服务截止日格式: ' + endDateStr);
                        }
                    }
                }
                
                // 处理区域字段
                if (singleProduct.containsKey('Region')) {
                    if (regionMap == null) { // 仅初始化一次，避免重复查询
                        // 调用getPicklistValues获取QuoteLineItem对象的Region__c字段的Picklist映射（标签→API值）
                        regionMap = getPicklistValues('QuoteLineItem', 'Region__c');
                        System.debug('动态初始化区域映射regionMap: ' + regionMap);
                    }
                    String regionStr = String.valueOf(singleProduct.get('Region'));
                    quoteLineItem.Region__c = regionMap.get(regionStr);
                }

                // 处理是否一次性费用字段
                if (singleProduct.containsKey('OneTimeFee')) {
                    String oneTimeFeeStr = String.valueOf(singleProduct.get('OneTimeFee'));
                    quoteLineItem.IS_OneTimeFee__c = String.isNotBlank(oneTimeFeeStr) ? oneTimeFeeStr : '否';
                } else {
                    quoteLineItem.IS_OneTimeFee__c = '否';
                }

                // 处理计费类型字段
                if (singleProduct.containsKey('chargeType')) {
                    String chargeTypeStr = String.valueOf(singleProduct.get('chargeType'));
                    quoteLineItem.Charge_Type__c = mapChargeTypeLabelToApiName(chargeTypeStr);
                } else {
                    quoteLineItem.Charge_Type__c = '2'; // 默认为计费
                }
               
                    if (quoteLineItem.Quantity == null) {
                        quoteLineItem.Quantity = 1;
                    }
                // PricebookEntryId
                if (quoteLineItem.Product2Id != null) {
                    if (productToPbEntry.containsKey(quoteLineItem.Product2Id)) {
                        quoteLineItem.PricebookEntryId = productToPbEntry.get(quoteLineItem.Product2Id);
                    } else {
                        missingPbProducts.add(String.valueOf(quoteLineItem.Product2Id));
                    }
                }
                insertQLIMap.put(objNo,quoteLineItem);
                objNo++;
                
            }
            if(insertQLIMap.size()>0){
                insert insertQLIMap.values();
            }

            objNo = 1;
            for (Object singleProductObj : singleProductsList) {
                 Map<String, Object> singleProduct = (Map<String, Object>)singleProductObj;
                
                // 确定当前选择的报价方式类型
                String selectedQuoteType = null;
                QuoteLineItem quoteLineItem = insertQLIMap.get(objNo);

                Quotation_Method__c qm = new Quotation_Method__c();

                // 产品折扣报价方式
                if (singleProduct.containsKey('hasProductDiscountQuote') &&
                    Boolean.valueOf(singleProduct.get('hasProductDiscountQuote'))) {

                    selectedQuoteType = '2';
                    String discountCoefficientStr = String.valueOf(singleProduct.get('discountCoefficient'));
                    String fixedRebateStr = String.valueOf(singleProduct.get('fixedRebate'));
                    String cashReduceStr = String.valueOf(singleProduct.get('cashReduce'));
                    String creditStr = String.valueOf(singleProduct.get('credit'));
                    Decimal discountCoefficient = String.isNotBlank(discountCoefficientStr) ? Decimal.valueOf(discountCoefficientStr) : 0;
                    Decimal fixedRebate = String.isNotBlank(fixedRebateStr) ? Decimal.valueOf(fixedRebateStr) : 0;
                    Decimal cashReduce = String.isNotBlank(cashReduceStr) ? Decimal.valueOf(cashReduceStr) : 0;
                    Decimal credit = String.isNotBlank(creditStr) ? Decimal.valueOf(creditStr) : 0;

                    // 保存产品折扣报价方式
                    Map<String, Object> fieldValues = new Map<String, Object>{
                        'Discount_Factor__c' => discountCoefficient,
                        'Fixed_Rebate__c' => fixedRebate,
                        'Cash_Reduce__c' => cashReduce,
                        'Credit__c' => credit
                    };

                    qm = saveSingleProductQuotationMethod(quoteLineItem, '2', fieldValues);
                }

               // 单价*数量
                if (singleProduct.containsKey('hasMinimumUnitPriceQuote') &&
                    Boolean.valueOf(singleProduct.get('hasMinimumUnitPriceQuote'))) {

                    selectedQuoteType = '1';
                    String minimumUnitPriceStr = String.valueOf(singleProduct.get('minimumUnitPrice'));
                    String minimumQuantityStr = String.valueOf(singleProduct.get('minimumQuantity'));
                    Decimal minimumUnitPrice = String.isNotBlank(minimumUnitPriceStr) ? Decimal.valueOf(minimumUnitPriceStr) : 0;
                    Decimal minimumQuantity = String.isNotBlank(minimumQuantityStr) ? Decimal.valueOf(minimumQuantityStr) : 0;

                    Map<String, Object> fieldValues = new Map<String, Object>{
                        'Minimum_ProdUnitPrice__c' => minimumUnitPrice,
                        'Minimum_Amout__c' => minimumQuantity
                    };

                    qm = saveSingleProductQuotationMethod(quoteLineItem, '1', fieldValues);
                }



                //处理阶梯报价方式 (共享阶梯金额折扣落区、3)
                // Boolean hasZone = (singleProduct.containsKey('hasSharedLadderAmountDiscountZone') && Boolean.valueOf(singleProduct.get('hasSharedLadderAmountDiscountZone'))); // 注释掉分区计算
                // Boolean hasAmountDown = (singleProduct.containsKey('hasSharedLadderAmountDiscountDown') && Boolean.valueOf(singleProduct.get('hasSharedLadderAmountDiscountDown')));
                Boolean hasUsageDown = (singleProduct.containsKey('hasSharedLadderUsagePriceDown') && Boolean.valueOf(singleProduct.get('hasSharedLadderUsagePriceDown')));
                Boolean hasMinAmountSharedLadder = (singleProduct.containsKey('hasMinAmountSharedLadderAmountDiscountDown') && Boolean.valueOf(singleProduct.get('hasMinAmountSharedLadderAmountDiscountDown')));
                Boolean hasMinUnitPriceSharedLadder = (singleProduct.containsKey('hasMinUnitPriceSharedLadderUsagePriceDown') && Boolean.valueOf(singleProduct.get('hasMinUnitPriceSharedLadderUsagePriceDown')));

                if (/* hasZone || */ hasUsageDown ) {
                    // String ladderMethod;
                    // if (hasZone) ladderMethod = '共享阶梯金额折扣分区'; // 注释掉分区计算
                    if (hasUsageDown) selectedQuoteType = '3';
                    // else ladderMethod = '3';

                    // 检查是否是组合报价方式，如果是，使用组合报价方式名称
                    // if (hasMinUnitPriceSharedLadder /* && hasUsageDown */) {
                    //     selectedQuoteType = '6';
                    // } else if (hasMinAmountSharedLadder /* && hasAmountDown*/) {
                    //     selectedQuoteType = '5';
                    // } else {
                    //     selectedQuoteType = ladderMethod;
                    // }

                    // 保存报价方式（使用完整的报价方式名称）
                    qm = saveSingleProductQuotationMethod(quoteLineItem, selectedQuoteType, new Map<String,Object>());
                    qm.LadderType__c = String.valueOf(singleProduct.get('ladderType'));

                   
                }

                // 处理保底金额+共享阶梯金额折扣落区组合报价方式
                if (hasMinAmountSharedLadder) {
                    String minimumAmountStr = String.valueOf(singleProduct.get('minimumAmount'));
                    Decimal minimumAmount = String.isNotBlank(minimumAmountStr) ? Decimal.valueOf(minimumAmountStr) : 0;

                    // 获取保底类型
                    String minimumGuaranteeType = '1'; // 默认为折前
                    if (singleProduct.containsKey('minimumGuaranteeType')) {
                        minimumGuaranteeType = String.valueOf(singleProduct.get('minimumGuaranteeType'));
                    }

                    Map<String, Object> fieldValues = new Map<String, Object>{
                        'GuaranteedMin_Amount__c' => minimumAmount,
                        'MinimumGuarantee_type__c' => minimumGuaranteeType
                    };

                    qm = saveSingleProductQuotationMethod(quoteLineItem, '4', fieldValues);
                    qm.LadderType__c = String.valueOf(singleProduct.get('ladderType'));


                }
                
                // 处理1+3组合报价方式
                if (hasMinUnitPriceSharedLadder) {
                    String minimumUnitPriceStr = String.valueOf(singleProduct.get('minimumUnitPrice'));
                    String minimumQuantityStr = String.valueOf(singleProduct.get('minimumQuantity'));
                    Decimal minimumUnitPrice = String.isNotBlank(minimumUnitPriceStr) ? Decimal.valueOf(minimumUnitPriceStr) : 0;
                    Decimal minimumQuantity = String.isNotBlank(minimumQuantityStr) ? Decimal.valueOf(minimumQuantityStr) : 0;
                    
                    Map<String, Object> fieldValues = new Map<String, Object>{
                        'Minimum_ProdUnitPrice__c' => minimumUnitPrice,
                        'Minimum_Amout__c' => minimumQuantity
                    };
                    
                    qm = saveSingleProductQuotationMethod(quoteLineItem, '5', fieldValues);
                    qm.LadderType__c = String.valueOf(singleProduct.get('ladderType'));

                }

                // 处理阶梯金额折扣报价方式
                Boolean hasSharedLadderAmountDiscountZone = singleProduct.containsKey('hasSharedLadderAmountDiscountZone') &&
                                                           Boolean.valueOf(singleProduct.get('hasSharedLadderAmountDiscountZone'));
                if (hasSharedLadderAmountDiscountZone) {
                    Map<String, Object> fieldValues = new Map<String, Object>();

                    qm = saveSingleProductQuotationMethod(quoteLineItem, '6', fieldValues);
                    qm.LadderType__c = String.valueOf(singleProduct.get('ladderType'));
                }

                insertQMList.add(qm);

                 // 处理新的阶梯行
                if (singleProduct.containsKey('tiers') && singleProduct.get('tiers') instanceof List<Object>) {
                    List<Object> tiersObj = (List<Object>) singleProduct.get('tiers');
                    System.debug('处理新阶梯数据，数量: ' + tiersObj.size() + ', 关联产品: ' + quoteLineItem.Id);

                    for (Object tierObj : tiersObj) {
                        if (!(tierObj instanceof Map<String, Object>)) continue;
                        Map<String, Object> tier = (Map<String, Object>) tierObj;
                        Ladder_Line__c line = new Ladder_Line__c();
                        // 设置数值
                        line.Down_Limit__c = tier.containsKey('lowerBound') ? Decimal.valueOf(String.valueOf(tier.get('lowerBound'))) : 0;
                        line.Up_Limit__c   = tier.containsKey('upperBound') ? Decimal.valueOf(String.valueOf(tier.get('upperBound'))) : 0;
                        line.Discount__c   = tier.containsKey('discount') ? Decimal.valueOf(String.valueOf(tier.get('discount'))) : 0;
                        line.Unit__c       = tier.containsKey('unit') ? String.valueOf(tier.get('unit')) : (hasUsageDown ? '用量' : '金额');
                        line.Calculation_Method__c = tier.containsKey('calculationMethod') ? String.valueOf(tier.get('calculationMethod')) : '落区'; // 默认使用落区，因为不再支持分区

                        System.debug('创建新阶梯: 下限=' + line.Down_Limit__c + ', 上限=' + line.Up_Limit__c + ', 折扣=' + line.Discount__c);

                        // 直接关联已有的QuoteLineItem
                        line.QuoteLineItem__c = quoteLineItem.Id;
                        insertLLList.add(line);
                    }
                }

                objNo++;



            }

            if(insertQMList.size()>0){
                insert insertQMList;
            }
            if(insertLLList.size()>0){
                insert insertLLList;
            }

            // 更新Quote对象的利润率字段
            updateQuoteProfitRate(quoteId);
            returnMsg.put('isSuccess', 'true');
            // return '成功保存单个产品数据';
        } catch (Exception e) {
            Database.rollback(spoint);
            String errorMsg = '保存失败: ' + e.getMessage() + ' 位置: ' + e.getLineNumber();
            System.debug(LoggingLevel.ERROR, '保存单个产品数据时发生异常: ' + errorMsg);
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            returnMsg.put('msg', errorMsg);

            // return errorMsg;

        }
        return returnMsg;



    }

    /**
     * 更新Quote对象的利润率字段
     * 查询该报价的所有QuoteLineItem的利润率Tax_Rate__c，获取最小的利润率，然后赋值给Quote对象的Profit_Rate__c字段
     * @param quoteId 报价单ID
     */
    public static void updateQuoteProfitRate(String quoteId) {
        try {
            System.debug('开始更新Quote利润率，报价ID: ' + quoteId);

            // 查询该报价下所有QuoteLineItem的利润率Tax_Rate__c
            List<QuoteLineItem> quoteLineItems = [
                SELECT Id, Tax_Rate__c
                FROM QuoteLineItem
                WHERE QuoteId = :quoteId
                AND Tax_Rate__c != null
            ];

            if (quoteLineItems.isEmpty()) {
                System.debug('没有找到包含利润率的QuoteLineItem，跳过更新');
                return;
            }

            // 找出最小的利润率
            Decimal minProfitRate = null;
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Tax_Rate__c != null) {
                    if (minProfitRate == null || qli.Tax_Rate__c < minProfitRate) {
                        minProfitRate = qli.Tax_Rate__c;
                    }
                }
            }

            System.debug('找到的最小利润率: ' + minProfitRate);

            if (minProfitRate != null) {
                // 更新Quote对象的Profit_Rate__c字段
                Quote quoteToUpdate = new Quote(
                    Id = Id.valueOf(quoteId),
                    Profit_Rate__c = minProfitRate
                );

                update quoteToUpdate;
                System.debug('成功更新Quote利润率: ' + minProfitRate);
            }

        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, '更新Quote利润率时发生异常: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            // 不抛出异常，避免影响主要的保存流程
        }
    }

   public static Quotation_Method__c saveSingleProductQuotationMethod(QuoteLineItem quoteLineItem, String methodType, Map<String, Object> fieldValues){

        // 先尝试查找现有报价方式
        Quotation_Method__c quotation = new Quotation_Method__c();
        quotation.Method__c = methodType;
        quotation.ISGROUP__c = false;
        // 如果 QuoteLineItem 已经有 Id，直接关联
        if (quoteLineItem.Id != null) {
            quotation.Quote_Line_Item_ID__c = quoteLineItem.Id;
        }
        // 设置其它字段
        for (String key : fieldValues.keySet()) {
            quotation.put(key, fieldValues.get(key));
        }
        return quotation;
        
        
    }

    /**
     * 删除单产品报价方式
     * @param quoteLineItem 报价行项目
     * @param existingQuotations 现有报价方式映射
     * @param processResult 处理结果对象
     */
    public static void deleteSingleProductQuotationMethod(QuoteLineItem quoteLineItem, Map<Id, Quotation_Method__c> existingQuotations, ProcessResult processResult) {
        try {
            if (quoteLineItem == null || quoteLineItem.Id == null) {
                return;
            }

            // 查找现有的报价方式
            if (existingQuotations.containsKey(quoteLineItem.Id)) {
                Quotation_Method__c quotationToDelete = existingQuotations.get(quoteLineItem.Id);
                processResult.quotationMethodsToDelete.add(quotationToDelete);
                System.debug('标记删除单产品报价方式: ' + quotationToDelete.Id + ', 类型: ' + quotationToDelete.Method__c + ', 关联产品: ' + quoteLineItem.Id);
            }
        } catch (Exception ex) {
            System.debug('删除单产品报价方式时发生异常: ' + ex.getMessage());
        }
    }

    /**
     * 删除单产品的所有其他报价方式（除了指定的报价方式类型）
     * @param quoteLineItem 报价行项目
     * @param keepMethodType 要保留的报价方式类型
     * @param existingAllQuotations 所有现有报价方式映射
     * @param processResult 处理结果对象
     */
    public static void deleteOtherSingleProductQuotationMethods(QuoteLineItem quoteLineItem, String keepMethodType, Map<Id, List<Quotation_Method__c>> existingAllQuotations, ProcessResult processResult) {
        try {
            if (quoteLineItem == null || quoteLineItem.Id == null) {
                return;
            }

            // 查找该产品的所有报价方式
            if (existingAllQuotations.containsKey(quoteLineItem.Id)) {
                List<Quotation_Method__c> allQuotations = existingAllQuotations.get(quoteLineItem.Id);
                for (Quotation_Method__c qm : allQuotations) {
                    // 删除除了指定类型之外的所有报价方式
                    if (qm.Method__c != keepMethodType) {
                        processResult.quotationMethodsToDelete.add(qm);
                        System.debug('标记删除其他单产品报价方式: ' + qm.Id + ', 类型: ' + qm.Method__c + ', 关联产品: ' + quoteLineItem.Id + ', 保留类型: ' + keepMethodType);
                    }
                }
            }
        } catch (Exception ex) {
            System.debug('删除其他单产品报价方式时发生异常: ' + ex.getMessage());
        }
    }
    
    /**
     * 获取单个产品数据
     * @param quoteId 报价单ID
     * @return 单个产品数据列表
     */
    @AuraEnabled(cacheable=false)
    public static List<Map<String, Object>> getSingleProducts(String quoteId) {
        System.debug('获取单个产品数据，报价ID: ' + quoteId);
        List<Map<String, Object>> result = new List<Map<String, Object>>();

        try {
            // 首先查询Quote的服务日期作为默认值
            Date quoteStartDate = null;
            Date quoteEndDate = null;
            for (Quote quote : [SELECT Id, StartDate__c, EndDate__c FROM Quote WHERE Id = :quoteId LIMIT 1]) {
                quoteStartDate = quote.StartDate__c;
                quoteEndDate = quote.EndDate__c;
                System.debug('Quote服务开始日: ' + quoteStartDate + ', 服务结束日: ' + quoteEndDate);
            }
            // 获取报价单下的单个产品
            // 修复：确保只查询真正的单产品（ISGROUP__c = false 且 Product_Group__c = null）
            List<QuoteLineItem> quoteLineItems = Database.query(
                'SELECT Id, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
                'Product2.Description, Product2.QuantityUnitOfMeasure, UnitPrice,ListPrice__c,' +
                'Tax_Rate__c, Account_ID__c, Profit_Statement__c, Description, ' +
                'QuoteLineStartDate__c, QuoteLineEndDate__c, toLabel(Region__c), toLabel(IS_OneTimeFee__c), toLabel(Charge_Type__c), ' +
                'Product_Group__c, ' +
                'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
                'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name ' +
                'FROM QuoteLineItem ' +
                'WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null AND Product2Id != null ' +
                'ORDER BY CreatedDate ASC'
            );

            System.debug('查询到的单产品数量: ' + quoteLineItems.size());

            // 过滤掉没有产品信息的记录
            List<QuoteLineItem> validQuoteLineItems = new List<QuoteLineItem>();
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Product2Id != null && qli.Product2 != null && String.isNotBlank(qli.Product2.Name)) {
                    validQuoteLineItems.add(qli);
                } else {
                    System.debug('过滤掉无效的单产品记录: ' + qli.Id + ', Product2Id: ' + qli.Product2Id);
                }
            }
            quoteLineItems = validQuoteLineItems;
            System.debug('过滤后的有效单产品数量: ' + quoteLineItems.size());

            // 收集所有产品ID，用于查询产品牌价
            //报价行Ids
            Set<Id> productIds = new Set<Id>();
            Set<Id> qliIds = new Set<Id>();
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Product2Id != null) {
                    productIds.add(qli.Product2Id);
                }
                qliIds.add(qli.Id);
            }
            
            // 查询产品牌价对象
            Map<String, Map<String, Object>> productToPriceMap = new Map<String, Map<String, Object>>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c,Unit__c, Amount__c, CurrencyIsoCode,toLabel(Product_Area__c)
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                Map<String, Object> priceInfo = new Map<String, Object>();
                priceInfo.put('amount', price.Amount__c);
                priceInfo.put('currencyCode', price.CurrencyIsoCode);
                // priceInfo.put('area', price.Product_Area__c);
                productToPriceMap.put(price.Product__c + price.Product_Area__c, priceInfo);
            }
            //查询报价方式
            Map<String,Quotation_Method__c> quoMethodMap = new Map<String,Quotation_Method__c>();
            for (Quotation_Method__c qm : [
                    SELECT Id, LadderType__c,toLabel(LadderType__c) LadderTypeLabel,Method__c,Quote_Line_Item_ID__c,toLabel(Method__c) MethodLabel,Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c,Minimum_ProdUnitPrice__c, Minimum_Amout__c,GuaranteedMin_Amount__c,MinimumGuarantee_type__c
                    FROM Quotation_Method__c
                    WHERE Quote_Line_Item_ID__c in :qliIds AND ISGROUP__c = false
            ]) {
                quoMethodMap.put(qm.Quote_Line_Item_ID__c,qm);
            }

            // 获取阶梯行数据
            Map<String,List<Ladder_Line__c>> ladderLineMap = new Map<String,List<Ladder_Line__c>>();
            List<Ladder_Line__c> ladderLines = [
                SELECT Id, QuoteLineItem__c,Down_Limit__c, Up_Limit__c, Unit__c, Discount__c, Calculation_Method__c
                FROM Ladder_Line__c
                WHERE QuoteLineItem__c in :qliIds
                ORDER BY Down_Limit__c ASC
            ];
            for (Ladder_Line__c line : ladderLines) {
                if(!ladderLineMap.containsKey(line.QuoteLineItem__c)){
                    ladderLineMap.put(line.QuoteLineItem__c,new List<Ladder_Line__c>());
                }
                ladderLineMap.get(line.QuoteLineItem__c).add(line);
            }


     
            
            // 处理每个单个产品
            for (QuoteLineItem qli : quoteLineItems) {
                Map<String, Object> singleProduct = new Map<String, Object>();
                singleProduct.put('id', qli.Id);
                singleProduct.put('productId', qli.Product2Id);
                singleProduct.put('productName', qli.Product2.Name);
                singleProduct.put('ProductCode', qli.Product2.ProductCode);
                singleProduct.put('Name', qli.Product2.Name);
                singleProduct.put('Family', qli.Product2.Family);
                singleProduct.put('Description', qli.Description);
                singleProduct.put('ProductDescription', qli.Product2.Description);
                singleProduct.put('QuantityUnitOfMeasure', qli.Product2.QuantityUnitOfMeasure);
                singleProduct.put('unitPrice', qli.UnitPrice);
                // singleProduct.put('listPrice', qli.ListPrice__c);
                singleProduct.put('taxRate', qli.Tax_Rate__c);
                singleProduct.put('customerAccountId', qli.Account_ID__c);
                singleProduct.put('profitDescription', qli.Profit_Statement__c);
                // 设置服务日期，如果产品没有设置日期则使用Quote的默认日期
                singleProduct.put('QuoteLineStartDate', qli.QuoteLineStartDate__c != null ? qli.QuoteLineStartDate__c : quoteStartDate);
                singleProduct.put('QuoteLineEndDate', qli.QuoteLineEndDate__c != null ? qli.QuoteLineEndDate__c : quoteEndDate);
                singleProduct.put('Region', qli.Region__c);
                singleProduct.put('OneTimeFee', qli.IS_OneTimeFee__c);
                singleProduct.put('chargeType', qli.Charge_Type__c);
                
                // 设置产品层级信息
                if (qli.Product2.Level__c == '4') {
                    singleProduct.put('level1', qli.Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name);
                    singleProduct.put('level2', qli.Product2.ParentProduct__r.ParentProduct__r.Name);
                    singleProduct.put('level3', qli.Product2.ParentProduct__r.Name);
                } else if (qli.Product2.Level__c == '3') {
                    singleProduct.put('level1', qli.Product2.ParentProduct__r.ParentProduct__r.Name);
                    singleProduct.put('level2', qli.Product2.ParentProduct__r.Name);
                    singleProduct.put('level3', qli.Product2.Name);
                }
                
                // 添加产品牌价信息
                if (productToPriceMap.containsKey(qli.Product2Id+qli.Region__c)) {
                    Map<String, Object> priceInfo = productToPriceMap.get(qli.Product2Id+qli.Region__c);
                    singleProduct.put('listPrice', priceInfo.get('amount'));
                    singleProduct.put('listPriceCurrency', priceInfo.get('currencyCode'));
                }
                
                // 查询单产品的报价方式
                // 固定金额报价方式 - 已注释掉
                // for (Quotation_Method__c qm : [
                //     SELECT Id, Method__c, Fixed_Dosage__c, Fixed_UnitPrice__c
                //     FROM Quotation_Method__c
                //     WHERE Quote_Line_Item_ID__c = :qli.Id AND Method__c = '固定金额' AND ISGROUP__c = false
                //     LIMIT 1
                // ]) {
                //     singleProduct.put('hasFixedAmountQuote', true);
                //     singleProduct.put('fixedUsage', qm.Fixed_Dosage__c);
                //     singleProduct.put('fixedAmount', qm.Fixed_UnitPrice__c);
                //     singleProduct.put('quoteTypeValue', '固定金额');
                // }
                
                // 产品折扣报价方式
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='2') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasProductDiscountQuote', true);
                    singleProduct.put('discountCoefficient', qm.Discount_Factor__c);
                    singleProduct.put('fixedRebate', qm.Fixed_Rebate__c);
                    singleProduct.put('cashReduce', qm.Cash_Reduce__c);
                    singleProduct.put('credit', qm.Credit__c);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                }
                // 1报价方式
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='1') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasMinimumUnitPriceQuote', true);
                    singleProduct.put('minimumUnitPrice', qm.Minimum_ProdUnitPrice__c);
                    singleProduct.put('minimumQuantity', qm.Minimum_Amout__c);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                }
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='共享阶梯金额折扣落区') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasSharedLadderAmountDiscountDown', true);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                }
                // 阶梯金额折扣报价方式
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='6') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasSharedLadderAmountDiscountZone', true);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                    singleProduct.put('ladderType', qm.LadderType__c);
                    singleProduct.put('ladderTypeLabel', qm.get('LadderTypeLabel'));
                }
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='3') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasSharedLadderUsagePriceDown', true);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                    singleProduct.put('ladderType', qm.LadderType__c);
                    singleProduct.put('ladderTypeLabel', qm.get('LadderTypeLabel'));
                }
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='4') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasMinAmountSharedLadderAmountDiscountDown', true);
                    singleProduct.put('minimumAmount', qm.GuaranteedMin_Amount__c);
                    singleProduct.put('minimumGuaranteeType', qm.MinimumGuarantee_type__c);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                    singleProduct.put('ladderType', qm.LadderType__c);
                    singleProduct.put('ladderTypeLabel', qm.get('LadderTypeLabel'));
                }
                if (quoMethodMap.containsKey(qli.id) && quoMethodMap.get(qli.id).Method__c =='5') {
                    Quotation_Method__c qm = quoMethodMap.get(qli.id);
                    singleProduct.put('hasMinUnitPriceSharedLadderUsagePriceDown', true);
                    singleProduct.put('minimumUnitPrice', qm.Minimum_ProdUnitPrice__c);
                    singleProduct.put('minimumQuantity', qm.Minimum_Amout__c);
                    singleProduct.put('quoteTypeValue', qm.Method__c);
                    singleProduct.put('quoteTypeLabel', qm.get('MethodLabel'));
                    singleProduct.put('ladderType', qm.LadderType__c);
                    singleProduct.put('ladderTypeLabel', qm.get('LadderTypeLabel'));
                }

                if (ladderLineMap.containsKey(qli.Id)) {
                    List<Map<String, Object>> tiers = new List<Map<String, Object>>();
                    for (Ladder_Line__c line : ladderLineMap.get(qli.Id)) {
                        Map<String, Object> tier = new Map<String, Object>();
                        tier.put('id', line.Id);
                        tier.put('lowerBound', line.Down_Limit__c);
                        tier.put('upperBound', line.Up_Limit__c);
                        tier.put('unit', line.Unit__c);
                        tier.put('discount', line.Discount__c);
                        tier.put('calculationMethod', line.Calculation_Method__c);
                        tiers.add(tier);
                    }
                    singleProduct.put('tiers', tiers);
                }

                result.add(singleProduct);
            }
            
            return result;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, '获取单个产品数据时发生异常: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            throw new AuraHandledException('获取单个产品数据失败: ' + e.getMessage()+e.getLineNumber());
        }
    }



    /**
     * 查询牌价
     * @param productId 产品ID
     * @param areaStr 区域
     */
    @AuraEnabled(cacheable=false)
    public static Map<String,String> getProductAndAreaListPrice(String productId,String areaStr) {
        Map<String,String> returnMsg = new Map<String,String>();
        returnMsg.put('isSuccess','false');
        returnMsg.put('msg','');
        Product2 pro = new Product2();
        try {
            if (regionMap == null) { // 仅初始化一次，避免重复查询
                // 调用getPicklistValues获取QuoteLineItem对象的Region__c字段的Picklist映射（标签→API值）
                regionMap = getPicklistValues('QuoteLineItem', 'Region__c');
                System.debug('动态初始化区域映射regionMap: ' + regionMap);
            }
            pro=[Select id,Name from Product2 where id=:productId];
            List<ProductPrice__c> proList = [select id,Product__r.Name,Amount__c,CurrencyIsoCode  from ProductPrice__c where Product__c=:productId and Product_Area__c=:regionMap.get(areaStr)];
            if (proList.size()>0) {
                returnMsg.put('isSuccess','true');
                returnMsg.put('amount',String.valueOf(proList[0].Amount__c));
                returnMsg.put('currencyCode',String.valueOf(proList[0].CurrencyIsoCode));

            }else{
                returnMsg.put('msg','产品：'+pro.Name + ' 区域：'+areaStr+' 无牌价！');
            }

        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, '更新Quote利润率时发生异常: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, '异常堆栈: ' + e.getStackTraceString());
            returnMsg.put('msg','产品：'+pro.Name + ' 区域：'+areaStr+' 查询牌价报错：'+e.getMessage());
        }
        return returnMsg;
    }

    /**
     * 将计费类型标签转换为API名称
     * @param chargeTypeLabel 计费类型标签
     * @return 计费类型API名称
     */
    private static String mapChargeTypeLabelToApiName(String chargeTypeLabel) {
       
        if (String.isBlank(chargeTypeLabel)) {
            return '2'; 
        }
        
        if (chargeTypeLabel == '计量') {
            return '1';
        } else if (chargeTypeLabel == '计费') {
            return '2';
        } else {
            return '2'; 
        }
    }

    /**
     * 计算并更新报价的合同收入
     * 累加所有保底金额和单价*数量的值
     */
    @AuraEnabled(cacheable=false)
    public static Map<String, Object> calculateAndUpdateContractRevenue(String quoteId) {
        Map<String, Object> result = new Map<String, Object>();

        try {
            Decimal totalRevenue = 0;

            // 1. 查询单产品的报价方式 - 保底金额和单价*数量
            List<Quotation_Method__c> singleProductMethods = [
                SELECT Id, Method__c, GuaranteedMin_Amount__c, Minimum_Price__c
                FROM Quotation_Method__c
                WHERE Quote_Line_Item_ID__c IN (
                    SELECT Id FROM QuoteLineItem
                    WHERE QuoteId = :quoteId AND ISGROUP__c = false
                )
                AND ISGROUP__c = false
                AND (Method__c = '1' OR Method__c = '5' OR Method__c = '6') // 1=单价*数量, 5=保底金额
            ];

            // 累加单产品的金额
            for (Quotation_Method__c method : singleProductMethods) {
                if (method.Method__c == '1' && method.Minimum_Price__c != null) {
                    // 单价*数量报价方式
                    totalRevenue += method.Minimum_Price__c;
                } else if (method.Method__c == '5' && method.GuaranteedMin_Amount__c != null) {
                    // 保底金额报价方式
                    totalRevenue += method.GuaranteedMin_Amount__c;
                }else if (method.Method__c == '6' && method.Minimum_Price__c != null) {
                    // 单价*数量+共享阶梯用量单价报价方式
                    totalRevenue += method.Minimum_Price__c;
                }
            }

            // 2. 查询产品组的报价方式 - 保底金额和单价*数量
            List<Quotation_Method__c> productGroupMethods = [
                SELECT Id, Method__c, GuaranteedMin_Amount__c, Minimum_Price__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (
                    SELECT Product_Group__c FROM QuoteLineItem
                    WHERE QuoteId = :quoteId AND ISGROUP__c = true
                )
                AND ISGROUP__c = true
                AND (Method__c = '1' OR Method__c = '5' OR Method__c = '6') // 1=单价*数量, 5=保底金额, 6=单价*数量+共享阶梯
            ];

            // 累加产品组的金额
            for (Quotation_Method__c method : productGroupMethods) {
                if (method.Method__c == '1' && method.Minimum_Price__c != null) {
                    // 单价*数量报价方式
                    totalRevenue += method.Minimum_Price__c;
                } else if (method.Method__c == '5' && method.GuaranteedMin_Amount__c != null) {
                    // 保底金额报价方式
                    totalRevenue += method.GuaranteedMin_Amount__c;
                } else if (method.Method__c == '6' && method.Minimum_Price__c != null) {
                    // 单价*数量+共享阶梯用量单价报价方式
                    totalRevenue += method.Minimum_Price__c;
                }
            }

            // 3. 更新Quote的Contract_Revenue__c字段
            Quote quoteToUpdate = new Quote(
                Id = Id.valueOf(quoteId),
                Contract_Revenue__c = totalRevenue
            );

            update quoteToUpdate;

            result.put('success', true);
            result.put('totalRevenue', totalRevenue);
            result.put('message', '合同收入计算完成，总收入: ' + totalRevenue);

            System.debug('合同收入计算完成 - QuoteId: ' + quoteId + ', 总收入: ' + totalRevenue);

        } catch (Exception e) {
            result.put('success', false);
            result.put('message', '计算合同收入失败: ' + e.getMessage());
            System.debug('计算合同收入失败: ' + e.getMessage());
            System.debug('错误堆栈: ' + e.getStackTraceString());
        }

        return result;
    }

     @AuraEnabled(cacheable=true)
    public static Map<String, String> getPicklistValues(String objectApiName, String fieldApiName) {
        Map<String, String> picklistMap = new Map<String, String>();

        Schema.SObjectType sObjectType = Schema.getGlobalDescribe().get(objectApiName);
        Schema.DescribeSObjectResult objDescribe = sObjectType.getDescribe();
        Schema.SObjectField field = objDescribe.fields.getMap().get(fieldApiName);
        if (sObjectType == null || field == null) {
            System.debug('错误：对象 API 名称不存在 - ' + objectApiName +'字段 API 名称不存在 - ' + fieldApiName);
            return picklistMap;
        }

        Schema.DescribeFieldResult fieldDescribe = field.getDescribe();
        for (Schema.PicklistEntry entry : fieldDescribe.getPicklistValues()) {
            if (entry.isActive()) { // 仅包含活跃选项
                picklistMap.put(entry.getLabel(),entry.getValue()); // Key: API值，Value: 显示标签
            }
        }
        return picklistMap;
    }
}