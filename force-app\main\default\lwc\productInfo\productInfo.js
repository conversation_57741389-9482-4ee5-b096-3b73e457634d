import { LightningElement, api, track, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import getProducts from '@salesforce/apex/ProductInfoController.getProducts';
import getProductsFilterList from '@salesforce/apex/ProductInfoController.getProductsFilterList';
import getMaaSFilterOptions from '@salesforce/apex/ProductInfoController.getMaaSFilterOptions';
import getProductsByLevels from '@salesforce/apex/ProductInfoController.getProductsByLevels';
import getMaaSProducts from '@salesforce/apex/ProductInfoController.getMaaSProducts';

export default class ProductInfo extends NavigationMixin(LightningElement) {
    @api recordId; // 当前记录ID
    @api groupIndex; // 来自URL的产品组索引
    @api quoteName; 
    @api quoteLevel;//报价产品大类
    @track products = []; // 产品列表
    @track filteredProducts = []; // 筛选后的产品列表
    @track showFilter = false; // 是否显示筛选面板
    @track isLoading = false; // 是否正在加载数据
    @track error; // 错误信息
    @track selectedRows = []; // 选中的行
    @track searchTerm = ''; // 搜索关键词
    @track selected = []; // 选中的产品
    @track isOtherTab = false; // 是否显示其他产品
    @track isMaaS = false; // 是否显示MaaS产品
    @track maasOpen = false; // 是否显示MaaS产品
    @track ProductOptions =[
        { label: 'MSP', value: 'MSP' },
        { label: 'AI Search', value: 'AI Search' },
        { label: 'AI基础设施', value: 'AI基础设施' },
        { label: 'MAAS', value: 'MAAS' },
        { label: '算力', value: '算力' },
        { label: '其他', value: '其他' },
        {label:'全部', value: '' }
    ];

    @track level2ProductCate=[
         {label:'全部', value: '' },
        { label: '边缘IP', value: '边缘IP' },
        { label: '云连接', value: '云连接' },
        { label: 'Smart Connect', value: 'Smart Connect' },
        { label: 'Smart CDN', value: 'Smart CDN' },
        { label: '智能算力裸金属', value: '智能算力裸金属' },
        { label: '智能算力云主机', value: '智能算力云主机' },
        { label: '智能算力块存储', value: '智能算力块存储' },
        { label: '智能算力对象存储', value: '智能算力对象存储' },
        { label: '智能算力网络存储', value: '智能算力网络存储' },
        { label: '智能算力 IP地址', value: '智能算力 IP地址' },
        { label: 'AIDC', value: 'AIDC' },
        { label: '智能算力测试', value: '智能算力测试' },
        { label: '智能算力流量包', value: '智能算力流量包' },
        { label: '智能算力带宽', value: '智能算力带宽' },
        { label: '专属模型服务教育场景服务', value: '专属模型服务教育场景服务' },
        { label: 'AI模型服务', value: 'AI模型服务' },
        { label: 'Marketplace', value: 'Marketplace' },
        { label: 'MDF', value: 'MDF' },
        { label: 'MSP服务', value: 'MSP服务' },
        { label: '公有云转售', value: '公有云转售' },
        { label: 'MSP自研产品', value: 'MSP自研产品' },
        { label: 'smartSearch', value: 'smartSearch' },
        { label: 'webRead', value: 'webRead' },
        { label: 'Serp', value: 'Serp' },
        { label: 'fullTextSearch', value: 'fullTextSearch' }
    ];

    @track levelOneProducts = [];
    @track levelTwoProduct = [];
    @track levelThreeProduct = [];
    // 筛选条件
    @track filterCriteria = {
        productCode: '',
        productName: '',
        family: ''
    };
    @track filterCriteria2 = {
        levelOne: '',
        levelTwo: '',
        levelThree: ''
    };
    
    
    // 表格列定义
    columns = [
        { label: '产品编码', fieldName: 'ProductCode', type: 'text' },
        { label: '产品名称-中文', fieldName: 'Name', type: 'text' },
        { label: '产品名称-英文', fieldName: 'Description', type: 'text' },
        { label: '一级产品', fieldName: 'level1', type: 'text' },
        { label: '二级产品', fieldName: 'level2', type: 'text' },
        { label: '三级产品', fieldName: 'level3', type: 'text' },
        { label: '计量单位', fieldName: 'QuantityUnitOfMeasure', type: 'text' }
    ];

    

    // 获取页面引用，用于获取URL参数
    @wire(CurrentPageReference)
    getPageReference(pageRef) {
        if (pageRef) {
            if (pageRef.state && pageRef.state.c__recordId) {
                this.recordId = pageRef.state.c__recordId;
                console.log('recordId:', this.recordId);
            }
            if (pageRef.state && pageRef.state.c__groupIndex) {
                this.groupIndex = pageRef.state.c__groupIndex;
                console.log('groupIndex:', this.groupIndex);
                
            }
        }
    }
    
    // 组件初始化完成后加载数据
    connectedCallback() {
        // if (this.groupIndex != 'single') {
        //     this.maasOpen = true;
        // }
        console.log('this.quoteLevel:',this.quoteLevel);
        if(this.quoteLevel =='MAAS'){
            this.maasOpen = true;
            this.isOtherTab = false;
        }else{
            this.maasOpen = false;
            this.isOtherTab = true;
        }
        this.loadProducts();
        this.loadMaaSFilterOptions();
    }
    
    loadMaaSFilterOptions() {
        getMaaSFilterOptions()
            .then(result => {
                this.levelOneProducts = result.levelOne;
                this.levelTwoProduct = result.levelTwo;
                this.levelThreeProduct = result.levelThree;
            })
            .catch(error => {
                this.error = error;
                console.error('Error loading MaaS filter options:', error);
            });
    }

    // 加载产品数据
    loadProducts() {
        this.isLoading = true;
        console.log('ProductInfo - 开始加载产品数据，recordId:', this.recordId);
        // 实际使用时，使用Apex方法获取产品数据
        getProducts({ quoteId: this.recordId })
            .then(result => {
                // 映射产品的多级名称
                this.products = result.map(prod => {
                    let level1 = '';
                    let level2 = '';
                    let level3 = '';
                    let level4 = '';

                    if (prod.Level__c === '4') {
                        //if(prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name =='MAAS'){this.maasOpen=true;}

                        level4 = prod.Name;
                        level3 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level2 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name : '';
                    } else if (prod.Level__c === '3') {
                        //if(prod.ParentProduct__r.ParentProduct__r.Name =='MAAS'){this.maasOpen=true;}

                        level3 = prod.Name;
                        level2 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                    }

                    return {
                        ...prod,
                        level1,
                        level2,
                        level3,
                        level4
                    };
                });
                this.error = undefined;
                console.log('ProductInfo - 加载产品数据成功，数量:', this.products.length);
            })
            .catch(error => {
                this.error = error;
                this.products = [];
                console.error('ProductInfo - 加载产品数据失败:', error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
    
    // 获取选中的产品并处理
    handleGetSelected() {
        if(this.isOtherTab){
            this.selected = this.template.querySelector('lightning-datatable').getSelectedRows();
            console.log('选中的产品:', JSON.stringify(this.selected));
                    
            if (this.selected.length === 0) {
                // 如果没有选中任何产品，显示提示
                alert('请至少选择一个产品');
                return;
            }
            // 处理选中的产品数据，将其转换为引用组件需要的格式
            const selectedProducts = this.selected.map(product => {
                return {
                    id: 'temp_product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    productId: product.Id,
                    productName: product.Name,
                    ProductCode: product.ProductCode,
                    Name: product.Name,
                    Family: product.Family,
                    Description: product.Description,
                    QuantityUnitOfMeasure: product.QuantityUnitOfMeasure,
                    taxRate: '0.00', // 设置默认税率为0.00
                    unitPrice: product.UnitPrice || '',
                    customerAccountId: '',
                    profitDescription: ''
                };
            });
            
            // 将选中的产品数据和产品组索引存储到localStorage
            localStorage.setItem('selectedProducts', JSON.stringify(selectedProducts));
            localStorage.setItem('productGroupIndex', this.groupIndex);
            
            // 关闭当前窗口
            // window.close();
            this.dispatchEvent(new CustomEvent('addproduct', {
                detail: {
                    productList: selectedProducts,
                    groupIndex: this.groupIndex
                }
            }));
        }else{
            this.isLoading = true;
            console.log('this.filterCriteria2.levelOne:',this.filterCriteria2.levelOne);
            console.log('this.filterCriteria2.levelTwo:',this.filterCriteria2.levelTwo);
            console.log('this.filterCriteria2.levelThree:',this.filterCriteria2.levelThree);
            getProductsByLevels({
                levelOneName: this.filterCriteria2.levelOne,
                levelTwoName: this.filterCriteria2.levelTwo,
                levelThreeName: this.filterCriteria2.levelThree
            })
            .then(result => {
                console.log('getProductsByLevels:',result.length);
                this.products = result.map(prod => {
                    let level1 = '', level2 = '', level3 = '', level4 = '';
                    if (prod.Level__c === '4') {
                        level4 = prod.Name;
                        level3 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level2 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name : '';
                    } else if (prod.Level__c === '3') {
                        level3 = prod.Name;
                        level2 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                    }
                    return { ...prod, level1, level2, level3, level4 };
                });
            this.products  = [...this.products];
            console.log('handleGetSelected this.products.length:',this.products.length);
            if (this.products.length === 0) {
                // 如果没有选中任何产品，显示提示
                alert('该产品类型下没有产品数据！');
                return;
            }

            // 处理Maas选择的产品类型的对应产品数据，将其转换为引用组件需要的格式
            const selectedProducts = this.products.map(product => {
                return {
                    id: 'temp_product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    productId: product.Id,
                    productName: product.Name,
                    ProductCode: product.ProductCode,
                    Name: product.Name,
                    Family: product.Family,
                    Description: product.Description,
                    QuantityUnitOfMeasure: product.QuantityUnitOfMeasure,
                    taxRate: '0.00', // 设置默认税率为0.00
                    unitPrice: product.UnitPrice || '',
                    customerAccountId: '',
                    profitDescription: ''
                };
            });
            
            // 将选中的产品数据和产品组索引存储到localStorage
            localStorage.setItem('selectedProducts', JSON.stringify(selectedProducts));
            localStorage.setItem('productGroupIndex', this.groupIndex);
            
            // 关闭当前窗口
            // window.close();
            this.dispatchEvent(new CustomEvent('addproduct', {
                detail: {
                    productList: selectedProducts,
                    groupIndex: this.groupIndex
                }
            }));

                    this.error = undefined;
                })
                .catch(error => {
                    this.error = error;
                    this.products = [];
                })
                .finally(() => {
                    this.isLoading = false;
                });
                

        } 

    }

    

    // 处理取消按钮点击
    handleCancel() {
        // 关闭当前窗口
        window.close();
    }

    // 处理搜索关键词变更
    handleSearchKeyChange(event) {
        this.searchTerm = event.target.value;
        // 实现搜索逻辑（可根据需要扩展）
    }

    // 清除搜索
    clearSearchCode() {
        const codeInput = this.template.querySelector('input[placeholder="产品编号"]');  
        if (codeInput) {
            codeInput.value = ''; 
             this.filterCriteria.productCode = '';
        }
    }
     clearSearchName() {    
        const nameInput = this.template.querySelector('input[placeholder="产品名称"]');     
        if (nameInput) {
            nameInput.value = ''; 
           this.filterCriteria.productName = ''; 
        }
    }
    productCodeChange(event) {
        this.filterCriteria.productCode = event.target.value;
        console.log('产品编号变更:', this.filterCriteria.productCode);
       
    }
    productNameChange(event) {
        this.filterCriteria.productName = event.target.value;
        console.log('产品名称变更:', this.filterCriteria.productName);
    }
    handleLevelOneProductChange(event) {
        this.filterCriteria2.levelOne = event.detail.value;
    }

    handleLevelTwoProductChange(event) {
        this.filterCriteria2.levelTwo = event.detail.value;
    }

    handleLevelThreeProductChange(event) {
        this.filterCriteria2.levelThree = event.detail.value;
    }

    async handleShowFilter2() {
        this.isLoading = true;
        console.log('this.filterCriteria2.levelOne:',this.filterCriteria2.levelOne);
        console.log('this.filterCriteria2.levelTwo:',this.filterCriteria2.levelTwo);
        console.log('this.filterCriteria2.levelThree:',this.filterCriteria2.levelThree);

        await getProductsByLevels({
            levelOneName: this.filterCriteria2.levelOne,
            levelTwoName: this.filterCriteria2.levelTwo,
            levelThreeName: this.filterCriteria2.levelThree
        })
        .then(result => {
            console.log('handleShowFilter2:',result.length);

            this.products = result.map(prod => {
                let level1 = '', level2 = '', level3 = '', level4 = '';
                if (prod.Level__c === '4') {
                    level4 = prod.Name;
                    level3 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                    level2 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                    level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name : '';
                } else if (prod.Level__c === '3') {
                    level3 = prod.Name;
                    level2 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                    level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                }
                return { ...prod, level1, level2, level3, level4 };
            });
            this.products  = [...this.products];
            console.log('handleShowFilter2 this.products.length:',this.products.length);

            this.error = undefined;
        })
        .catch(error => {
            this.error = error;
            this.products = [];
        })
        .finally(() => {
            this.isLoading = false;
        });
    }
    // 显示查询
    handleShowFilter() {
        this.isLoading = true;

        // 检查recordId是否存在
        if (!this.recordId) {
            console.error('报价单ID为空，无法执行过滤查询');
            this.error = '报价单ID为空，无法执行过滤查询';
            this.isLoading = false;
            return;
        }

        console.log('开始过滤查询，参数:', {
            quoteId: this.recordId,
            productCode: this.filterCriteria.productCode,
            productName: this.filterCriteria.productName,
            family: this.filterCriteria.family
        });

        getProductsFilterList({
            quoteId: this.recordId,
            productCode: this.filterCriteria.productCode,
            productName: this.filterCriteria.productName,
            family: this.filterCriteria.family
        })
            .then(result => {
                this.products = result.map(prod => {
                    let level1 = '';
                    let level2 = '';
                    let level3 = '';
                    let level4 = '';

                    if (prod.Level__c === '4') {
                        level4 = prod.Name;
                        level3 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level2 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name : '';
                    } else if (prod.Level__c === '3') {
                        level3 = prod.Name;
                        level2 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                    }

                    return {
                        ...prod,
                        level1,
                        level2,
                        level3,
                        level4
                    };
                });
                this.error = undefined;
                console.log('ProductInfo - 加载产品数据成功，数量:', this.products.length);
                //  this.filteredProducts = [...result];
            })
            .catch(error => {
                this.error = error;
                this.products = [];
                console.error('ProductInfo - 加载产品数据失败:', error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
    handleProductChange(event) {
        this.filterCriteria.family = event.detail.value;
    }
    // 获取是否有产品被选中
    get noProductsSelected() {
        const selectedRows = this.template.querySelector('lightning-datatable')?.getSelectedRows();
        return !selectedRows || selectedRows.length === 0;
    }

    // 获取是否选择产品类型
    get noMaaSProductsSelected() {
        const selectedRows = this.filterCriteria2.levelOne != '' && this.filterCriteria2.levelTwo != '' && this.filterCriteria2.levelThree !='';
        return !selectedRows;
    }


    handleOtherTabSelect(event){
        this.isOtherTab = true;
        this.isMaaS = false;
        this.products = [];
        this.loadProducts();
        console.log('handleOtherTabSelect',this.isOtherTab);
        console.log('handleMaaSTabSelect',this.isMaaS);
    }
    handleMaaSTabSelect(event){
        this.isMaaS = true;
        this.isOtherTab = false;
        this.products = [];
        this.loadMaaSProducts();
        console.log('handleOtherTabSelect 111',this.isOtherTab);
        console.log('handleMaaSTabSelect 111',this.isMaaS);
    }
   
    loadMaaSProducts() {
        this.isLoading = true;
        getMaaSProducts()
            .then(result => {
                this.products = result.map(prod => {
                    let level1 = '', level2 = '', level3 = '', level4 = '';
                    if (prod.Level__c === '4') {
                        level4 = prod.Name;
                        level3 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level2 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name : '';
                    } else if (prod.Level__c === '3') {
                        level3 = prod.Name;
                        level2 = prod.ParentProduct__r ? prod.ParentProduct__r.Name : '';
                        level1 = prod.ParentProduct__r && prod.ParentProduct__r.ParentProduct__r ? prod.ParentProduct__r.ParentProduct__r.Name : '';
                    }
                    return { ...prod, level1, level2, level3, level4 };
                });
                  console.log('MAASProductInfo - 加载产品数据成功，数量:', this.products.length);
            })
            .catch(error => {
                this.error = error;
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
}