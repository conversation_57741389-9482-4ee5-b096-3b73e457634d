public with sharing class QuoteStatusResetController {
    /**
     * LWC调用：将报价状态重置为草稿，并提示重新提交审批
     * @param quoteId 报价记录ID
     * @return 成功消息或错误信息
     */
    @AuraEnabled(cacheable=false) 
    public static String resetQuoteToDraft(String quoteId) { 
        if (String.isBlank(quoteId)) {
            throw new AuraHandledException('错误：报价ID不能为空');
        }
        List<Quote> quotes = [
            SELECT Id, Name, ApprovalStatus__c, Status 
            FROM Quote 
            WHERE Id = :quoteId 
            LIMIT 1
        ];
        if (quotes.isEmpty()) {
            throw new AuraHandledException('错误：未找到ID为 ' + quoteId + ' 的报价记录');
        }
        Quote quoteToUpdate = quotes[0];       
    
        quoteToUpdate.ApprovalStatus__c = 'Draft'; 
        quoteToUpdate.Status = '草稿'; 
        try {
            update quoteToUpdate;
            return '成功：报价 "' + quoteToUpdate.Name + '" 已重置为草稿状态，请重新提交审批流程。';
        } catch (DmlException e) {
            throw new AuraHandledException('更新失败：' + e.getDmlMessage(0));
        } catch (Exception e) {

            throw new AuraHandledException('系统错误：' + e.getMessage());
        }
    }
}