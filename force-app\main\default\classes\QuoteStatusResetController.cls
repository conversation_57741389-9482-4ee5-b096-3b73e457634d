public with sharing class QuoteStatusResetController {
    /**
     * LWC调用：将报价状态重置为草稿，并提示重新提交审批
     * @param quoteId 报价记录ID
     * @return 成功消息或错误信息
     */
    @AuraEnabled(cacheable=false)
    public static String resetQuoteToDraft(String quoteId) {
        System.debug('=== resetQuoteToDraft 开始执行 ===');
        System.debug('传入的quoteId: ' + quoteId);

        try {
            if (String.isBlank(quoteId)) {
                System.debug('错误：报价ID为空');
                throw new AuraHandledException('错误：报价ID不能为空');
            }

            List<Quote> quotes = [
                SELECT Id, Name, ApprovalStatus__c, Status
                FROM Quote
                WHERE Id = :quoteId
                LIMIT 1
            ];

            System.debug('查询到的报价记录数量: ' + quotes.size());

            if (quotes.isEmpty()) {
                System.debug('错误：未找到报价记录');
                throw new AuraHandledException('错误：未找到ID为 ' + quoteId + ' 的报价记录');
            }

            Quote quoteToUpdate = quotes[0];
            System.debug('原始报价状态 - ApprovalStatus__c: ' + quoteToUpdate.ApprovalStatus__c + ', Status: ' + quoteToUpdate.Status);

            // 设置状态为Draft，让工作流程处理Status字段的更新
            quoteToUpdate.ApprovalStatus__c = 'Draft';
            // 不直接设置Status字段，让工作流程来处理

            System.debug('准备更新报价状态 - ApprovalStatus__c: ' + quoteToUpdate.ApprovalStatus__c);

            // 使用标准update，让工作流程正常执行
            update quoteToUpdate;

            // 重新查询以获取最新状态（包括工作流程更新后的状态）
            Quote updatedQuote = [
                SELECT Id, Name, ApprovalStatus__c, Status
                FROM Quote
                WHERE Id = :quoteId
                LIMIT 1
            ];

            System.debug('更新后的报价状态 - ApprovalStatus__c: ' + updatedQuote.ApprovalStatus__c + ', Status: ' + updatedQuote.Status);

            String successMessage = '成功：报价 "' + updatedQuote.Name + '" 已重置为草稿状态，请重新提交审批流程。';
            System.debug('返回成功消息: ' + successMessage);
            return successMessage;

        } catch (DmlException e) {
            System.debug('DML异常: ' + e.getMessage());
            System.debug('DML异常详情: ' + e.getDmlMessage(0));
            System.debug('异常堆栈: ' + e.getStackTraceString());
            throw new AuraHandledException('更新失败：' + e.getDmlMessage(0));
        } catch (Exception e) {
            System.debug('系统异常: ' + e.getMessage());
            System.debug('异常堆栈: ' + e.getStackTraceString());
            throw new AuraHandledException('系统错误：' + e.getMessage());
        } finally {
            System.debug('=== resetQuoteToDraft 执行结束 ===');
        }
    }
}